import axios from 'axios';
import { toast } from 'react-hot-toast';
import { isTokenExpired } from '../utils/auth';

// Store reference to access Redux state
let store = null;

// Function to set store reference (called from main.jsx)
export const setApiStore = (storeInstance) => {
  store = storeInstance;
};

// Create axios instance
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:5001/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Define auth-related routes to prevent redirection from them
const AUTH_ENDPOINTS = [
  '/auth/login',
  '/admin/auth/login',
  '/auth/register',
  '/auth/forgot-password',
  '/auth/reset-password',
  '/admin/auth/change-password'
];

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    // First try to get token from Redux store, then fall back to localStorage
    let token = null;
    
    if (store) {
      const state = store.getState();
      token = state.auth?.token || state.adminAuth?.token;
    }
    
    // Fall back to localStorage if no Redux token found
    if (!token) {
      const adminToken = localStorage.getItem('adminToken');
      const userToken = localStorage.getItem('token');
      token = adminToken || userToken;
    }

    // Check if token is expired
    if (token && isTokenExpired(token)) {
      // Don't dispatch token expired for payment verification endpoints
      const isPaymentVerification = config.url?.includes('/verify/') || 
                                   config.url?.includes('/payment/verify') ||
                                   window.location.search.includes('reference=');
      
      if (!isPaymentVerification) {
        // Clear tokens
        localStorage.removeItem('adminToken');
        localStorage.removeItem('token');
        localStorage.removeItem('adminTokenExp');
        localStorage.removeItem('userType');
        
        // Dispatch token expired action if store exists
        if (store) {
          store.dispatch({ type: 'auth/tokenExpired' });
        }
      } else {
        console.log('Skipping token expired dispatch for payment verification');
      }
      
      // Don't add expired token to request
      return config;
    }
    
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    const { response, config } = error;
    
    // Check if this is an auth endpoint - don't redirect from auth endpoints
    const isAuthEndpoint = AUTH_ENDPOINTS.some(endpoint => 
      config?.url?.includes(endpoint)
    );
    
    if (response && !isAuthEndpoint) {
      const { status, data } = response;
      
      switch (status) {
        case 401:
          // Token expired or invalid
          console.log('401 error intercepted for URL:', config?.url);
          
          // Don't auto-redirect for payment verification endpoints
          const isPaymentVerification = config?.url?.includes('/verify/') || 
                                       config?.url?.includes('/payment/verify') ||
                                       config?.url?.includes('reference=');
          
          if (!isPaymentVerification) {
            if (store) {
              store.dispatch({ type: 'auth/tokenExpired' });
            }
            
            // Clear tokens
            localStorage.removeItem('adminToken');
            localStorage.removeItem('token');
            localStorage.removeItem('adminTokenExp');
            localStorage.removeItem('userType');
            
            // Only show error message if not on login page and not an auth endpoint
            if (window.location.pathname !== '/admin/login' && !isAuthEndpoint) {
              toast.error('Session expired. Please login again.');
              
              // Redirect to login after a short delay
              setTimeout(() => {
                window.location.href = '/login';
              }, 1500);
            }
          } else {
            console.log('Skipping auto-redirect for payment verification endpoint');
          }
          break;
          
        case 403:
          // Forbidden - clear tokens but don't redirect
          localStorage.removeItem('adminToken');
          localStorage.removeItem('token');
          localStorage.removeItem('adminTokenExp');
          localStorage.removeItem('userType');
          
          if (window.location.pathname !== '/admin/login' && !isAuthEndpoint) {
            toast.error(data.message || 'Access denied. Please login again.');
          }
          break;
          
        case 423:
          // Account locked
          toast.error(data.message || 'Account is temporarily locked');
          break;
          
        case 429:
          // Rate limited
          toast.error('Too many requests. Please try again later.');
          break;
          
        case 500:
          toast.error('Server error. Please try again later.');
          break;
          
        default:
          // Don't show toast for other errors as they're handled in components
          break;
      }
    } else if (error.code === 'NETWORK_ERROR' || !error.response) {
      toast.error('Network error. Please check your connection.');
    }
    
    return Promise.reject(error);
  }
);

export default api;

// Subscription service methods
export const subscriptionService = {
  // Get subscription status
  getSubscriptionStatus: async () => {
    try {
      const response = await api.get('/subscriptions/status');
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Initiate subscription payment
  initiatePayment: async () => {
    try {
      const response = await api.post('/subscriptions/payment/initiate');
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Verify subscription payment (new enhanced flow)
  verifySubscription: async (reference) => {
    try {
      const response = await api.post('/subscriptions/verify-subscription', {
        paymentReference: reference // Send as payment reference since that's what we get from URL
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Verify pending subscription without reference
  verifyPendingSubscription: async () => {
    try {
      const response = await api.post('/subscriptions/verify-pending');
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Cancel pending subscription
  cancelPending: async () => {
    try {
      const response = await api.post('/subscriptions/cancel-pending');
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Legacy verify payment (kept for compatibility)
  verifyPayment: async (paymentReference) => {
    try {
      const response = await api.get(`/subscriptions/payment/verify/${paymentReference}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Cancel subscription payment
  cancelPayment: async (paymentReference) => {
    try {
      const response = await api.post(`/subscriptions/payment/${paymentReference}/cancel`);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Get payment history
  getPaymentHistory: async (limit = 5) => {
    try {
      const response = await api.get(`/subscriptions/payment-history?limit=${limit}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Check profile toggle permission
  checkProfileTogglePermission: async () => {
    try {
      const response = await api.get('/subscriptions/profile-toggle-permission');
      return response.data;
    } catch (error) {
      throw error;
    }
  }
};
