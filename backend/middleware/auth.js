const jwt = require('jsonwebtoken');
const User = require('../models/User');
const Barber = require('../models/Barber');
const Admin = require('../models/Admin');
const { isIpRateLimitEnabled, isOtpRateLimitEnabled } = require('../config/security');
const { AppError } = require('../utils/errorHandlers');

// Generate JWT token
const generateToken = (userId, role) => {
  return jwt.sign(
    { 
      userId: userId,
      role: role 
    },
    process.env.JWT_SECRET,
    { 
      expiresIn: process.env.JWT_EXPIRE || '7d',
      issuer: 'etch-platform',
      audience: 'etch-users'
    }
  );
};

// Verify JWT token middleware
const authenticateToken = async (req, res, next) => {
  try {
    // Get token from header
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.startsWith('Bearer ')
      ? authHeader.substring(7)
      : null;

    if (!token) {
      return next(new AppError('Access denied. No token provided.', 401));
    }

    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET, {
      issuer: 'etch-platform',
      audience: 'etch-users'
    });

    let user = null;

    // Check if it's a user, barber, or admin based on role in token
    if (decoded.role === 'user') {
      user = await User.findById(decoded.userId).select('-password -otp');

      if (!user) {
        return next(new AppError('Invalid token. User not found.', 401));
      }

      // Check if user account is banned or suspended
      if (user.status === 'banned' || user.status === 'suspended') {
        return next(new AppError(`Your account has been ${user.status}. Please contact support for assistance.`, 403));
      }

      // Check if user account is active
      if (user.status !== 'verified' && user.status !== 'active') {
        return next(new AppError('Account not verified. Please verify your email first.', 401));
      }

      // Check if account is locked
      if (user.isAccountLocked()) {
        const lockTime = Math.ceil((user.security.accountLockedUntil - new Date()) / 60000);
        return next(new AppError(`Account is temporarily locked. Try again in ${lockTime} minutes.`, 423));
      }

    } else if (decoded.role === 'barber') {
      user = await Barber.findById(decoded.userId).select('-password -otp');

      if (!user) {
        return next(new AppError('Invalid token. Barber not found.', 401));
      }

      // Check if barber account is banned or suspended (check both status fields)
      if (user.status === 'banned' || user.status === 'suspended' || 
          user.registrationStatus === 'banned' || user.registrationStatus === 'suspended') {
        return next(new AppError('Your account has been restricted. Please contact support for assistance.', 403));
      }

      // Check barber account status
      if (user.registrationStatus === 'pending_verification') {
        return next(new AppError('Account under review. Please wait for admin approval.', 401));
      }

      if (user.registrationStatus !== 'verified') {
        return next(new AppError('Account not verified. Please complete registration process.', 401));
      }

      // Check if account is locked
      if (user.isAccountLocked()) {
        const lockTime = Math.ceil((user.security.accountLockedUntil - new Date()) / 60000);
        return next(new AppError(`Account is temporarily locked. Try again in ${lockTime} minutes.`, 423));
      }

    } else if (decoded.role === 'admin' || decoded.role === 'super_admin') {
      user = await Admin.findById(decoded.userId).select('-password -otp');

      if (!user) {
        return next(new AppError('Invalid token. Admin not found.', 401));
      }

      // Check admin account status
      if (user.status !== 'active') {
        return next(new AppError(`Admin account is ${user.status}. Please contact system administrator.`, 401));
      }

      // Check if account is locked
      if (user.isAccountLocked()) {
        const lockTime = Math.ceil((user.security.accountLockedUntil - new Date()) / 60000);
        return next(new AppError(`Admin account is temporarily locked. Try again in ${lockTime} minutes.`, 423));
      }

      // Update last activity for admin
      user.lastActivity = new Date();
      await user.save();
    }

    if (!user) {
      return next(new AppError('Invalid token. User not found.', 401));
    }

    // Check if token was issued before password change (token invalidation)
    if (user.security.passwordChangedAt) {
      const tokenIssuedAt = new Date(decoded.iat * 1000); // Convert from seconds to milliseconds
      if (tokenIssuedAt < user.security.passwordChangedAt) {
        return next(new AppError('Token is no longer valid. Please login again.', 401));
      }
    }

    // Add role from token to user object for authorization middleware
    user.role = decoded.role;

    // Add user to request object
    req.user = user;
    next();
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      return next(new AppError('Token has expired. Please login again.', 401));
    }

    if (error.name === 'JsonWebTokenError') {
      return next(new AppError('Invalid token.', 401));
    }

    return next(new AppError('Authentication failed.', 500));
  }
};

// Role-based authorization middleware
const auth = (...roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return next(new AppError('Authentication required.', 401));
    }

    // Flatten roles array in case it's nested
    const flatRoles = roles.flat();
    
    if (!flatRoles.includes(req.user.role)) {
      return next(new AppError('You do not have permission to perform this action.', 403));
    }

    next();
  };
};

// Optional authentication middleware
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.startsWith('Bearer ')
      ? authHeader.substring(7)
      : null;

    if (!token) {
      return next();
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    let user = null;

    if (decoded.role === 'user') {
      user = await User.findById(decoded.userId).select('-password -otp');
    } else if (decoded.role === 'barber') {
      user = await Barber.findById(decoded.userId).select('-password -otp');
    } else if (decoded.role === 'admin') {
      user = await Admin.findById(decoded.userId).select('-password -otp');
    }

    if (user) {
      // Add role from token to user object for authorization middleware
      user.role = decoded.role;
      req.user = user;
    }

    next();
  } catch (error) {
    // If token is invalid, just proceed without user
    next();
  }
};

// Rate limiting middleware for authentication endpoints
const authRateLimit = {
  // Track login attempts per IP
  loginAttempts: new Map(),

  // Check login rate limit
  checkLoginLimit: (req, res, next) => {
    // Skip rate limiting if disabled via environment variable
    if (!isIpRateLimitEnabled()) {
      return next();
    }

    const ip = req.ip || req.connection.remoteAddress;
    const now = Date.now();
    const windowMs = 15 * 60 * 1000; // 15 minutes
    const maxAttempts = 5;

    if (!authRateLimit.loginAttempts.has(ip)) {
      authRateLimit.loginAttempts.set(ip, { count: 1, resetTime: now + windowMs });
      return next();
    }

    const attempts = authRateLimit.loginAttempts.get(ip);

    if (now > attempts.resetTime) {
      // Reset the counter
      authRateLimit.loginAttempts.set(ip, { count: 1, resetTime: now + windowMs });
      return next();
    }

    if (attempts.count >= maxAttempts) {
      const remainingTime = Math.ceil((attempts.resetTime - now) / 60000);
      return res.status(429).json({
        success: false,
        message: `Too many login attempts from this IP. Please try again in ${remainingTime} minutes.`,
        code: 'IP_RATE_LIMIT'
      });
    }

    attempts.count++;
    next();
  },

  // Reset login attempts on successful login
  resetLoginAttempts: (req) => {
    const ip = req.ip || req.connection.remoteAddress;
    authRateLimit.loginAttempts.delete(ip);
  },

  // Check OTP rate limit (5 attempts per 15 minutes)
  checkOTPLimit: (req, res, next) => {
    // Skip OTP rate limiting if disabled via environment variable
    if (!isOtpRateLimitEnabled()) {
      return next();
    }

    const ip = req.ip || req.connection.remoteAddress;
    const otpKey = `otp_${ip}`;
    const now = Date.now();
    const windowMs = 15 * 60 * 1000; // 15 minutes
    const maxAttempts = 5;

    if (!authRateLimit.loginAttempts.has(otpKey)) {
      authRateLimit.loginAttempts.set(otpKey, { count: 1, resetTime: now + windowMs });
      return next();
    }

    const attempts = authRateLimit.loginAttempts.get(otpKey);

    if (now > attempts.resetTime) {
      authRateLimit.loginAttempts.set(otpKey, { count: 1, resetTime: now + windowMs });
      return next();
    }

    if (attempts.count >= maxAttempts) {
      const remainingTime = Math.ceil((attempts.resetTime - now) / 60000);
      return res.status(429).json({
        success: false,
        message: `Too many OTP attempts. Please try again in ${remainingTime} minutes.`,
        code: 'OTP_RATE_LIMIT'
      });
    }

    attempts.count++;
    next();
  }
};

// Middleware to check if user needs OTP verification
const requireOTPVerification = async (req, res, next) => {
  try {
    const { email } = req.body;
    const user = await User.findByEmail(email);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found.'
      });
    }

    if (user.status !== 'verified') {
      return res.status(400).json({
        success: false,
        message: 'Account not verified. Please verify your email first.',
        requiresVerification: true,
        userId: user._id
      });
    }

    req.user = user;
    next();
  } catch (error) {
    console.error('OTP verification check error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error during verification check.'
    });
  }
};

// Middleware to extract user info from token without strict verification
const extractUserInfo = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.startsWith('Bearer ') 
      ? authHeader.substring(7) 
      : null;

    if (token) {
      try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        req.tokenData = decoded;
      } catch (error) {
        // Token invalid or expired, continue without user data
        req.tokenData = null;
      }
    }

    next();
  } catch (error) {
    next();
  }
};

// Function to invalidate all JWT tokens for a user
// This is achieved by updating the user's passwordChangedAt field
// The authenticateToken middleware can check if the token was issued before this date
const invalidateUserTokens = async (userId) => {
  const user = await User.findById(userId);
  if (user) {
    user.security.passwordChangedAt = new Date();
    await user.save();
  }
};

module.exports = {
  generateToken,
  authenticateToken,
  auth,
  optionalAuth,
  authRateLimit,
  requireOTPVerification,
  extractUserInfo,
  invalidateUserTokens
};
