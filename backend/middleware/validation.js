const Joi = require('joi');
const { isOtpRateLimitEnabled } = require('../config/security');

// User registration validation schema
const userRegistrationSchema = Joi.object({
  fullName: Joi.string()
    .min(2)
    .max(50)
    .trim()
    .required()
    .pattern(/^[a-zA-Z\s]+$/)
    .messages({
      'string.empty': 'Full name is required',
      'string.min': 'Full name must be at least 2 characters long',
      'string.max': 'Full name cannot exceed 50 characters',
      'string.pattern.base': 'Full name can only contain letters and spaces',
      'any.required': 'Full name is required'
    }),

  email: Joi.string()
    .email({ tlds: { allow: false } })
    .lowercase()
    .trim()
    .required()
    .messages({
      'string.empty': 'Email is required',
      'string.email': 'Please provide a valid email address',
      'any.required': 'Email is required'
    }),

  password: Joi.string()
    .min(8)
    .max(128)
    .pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$/)
    .required()
    .messages({
      'string.empty': 'Password is required',
      'string.min': 'Password must be at least 8 characters long',
      'string.max': 'Password cannot exceed 128 characters',
      'string.pattern.base': 'Password must contain at least one uppercase letter, one lowercase letter, and one number',
      'any.required': 'Password is required'
    }),

  confirmPassword: Joi.string()
    .valid(Joi.ref('password'))
    .required()
    .messages({
      'any.only': 'Confirm password must match the password',
      'any.required': 'Confirm password is required'
    })
});

// Login validation schema
const loginSchema = Joi.object({
  email: Joi.string()
    .email({ tlds: { allow: false } })
    .lowercase()
    .trim()
    .required()
    .messages({
      'string.empty': 'Email is required',
      'string.email': 'Please provide a valid email address',
      'any.required': 'Email is required'
    }),
  
  password: Joi.string()
    .required()
    .messages({
      'string.empty': 'Password is required',
      'any.required': 'Password is required'
    }),

  userType: Joi.string()
    .valid('user', 'barber')
    .required()
    .messages({
      'string.empty': 'User type is required',
      'any.only': 'User type must be either "user" or "barber"',
      'any.required': 'User type is required'
    })
});

// OTP verification validation schema
const otpVerificationSchema = Joi.object({
  email: Joi.string()
    .email({ tlds: { allow: false } })
    .lowercase()
    .trim()
    .required()
    .messages({
      'string.empty': 'Email is required',
      'string.email': 'Please provide a valid email address',
      'any.required': 'Email is required'
    }),
  
  otp: Joi.string()
    .length(6)
    .pattern(/^\d{6}$/)
    .required()
    .messages({
      'string.empty': 'OTP is required',
      'string.length': 'OTP must be exactly 6 digits',
      'string.pattern.base': 'OTP must contain only numbers',
      'any.required': 'OTP is required'
    })
});

// OTP resend validation schema
const otpResendSchema = Joi.object({
  email: Joi.string()
    .email({ tlds: { allow: false } })
    .lowercase()
    .trim()
    .required()
    .messages({
      'string.empty': 'Email is required',
      'string.email': 'Please provide a valid email address',
      'any.required': 'Email is required'
    })
});

// Validation middleware function
const validate = (schema) => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.body, {
      abortEarly: false, // Return all validation errors
      stripUnknown: true // Remove unknown fields
    });

    if (error) {
      const errors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context?.value,
        label: detail.context?.label
      }));

      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors
      });
    }

    // Replace req.body with validated and sanitized data
    req.body = value;
    next();
  };
};

// Rate limiting validation for OTP attempts
const validateOTPAttempts = (user) => {
  // Skip OTP rate limiting if disabled via environment variable
  if (!isOtpRateLimitEnabled()) {
    return { valid: true };
  }

  const now = new Date();
  const lastAttempt = user.otp.lastAttempt;

  // Check if user has exceeded maximum attempts
  if (user.otp.attempts >= 5) {
    // Check if 15 minutes have passed since last attempt
    if (lastAttempt && (now - lastAttempt) < 15 * 60 * 1000) {
      const remainingTime = Math.ceil((15 * 60 * 1000 - (now - lastAttempt)) / 60000);
      return {
        valid: false,
        message: `Too many OTP attempts. Please try again in ${remainingTime} minutes.`
      };
    } else {
      // Reset attempts after 15 minutes
      user.otp.attempts = 0;
    }
  }

  return { valid: true };
};

// Email format validation helper
const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Password strength validation helper
const isStrongPassword = (password) => {
  // At least 8 characters, one uppercase, one lowercase, one number
  const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$/;
  return passwordRegex.test(password);
};

// Sanitize user input
const sanitizeInput = (input) => {
  if (typeof input === 'string') {
    return input.trim().replace(/[<>]/g, '');
  }
  return input;
};

// Custom validation middleware for specific use cases
const customValidation = {
  // Check if passwords match
  passwordsMatch: (req, res, next) => {
    const { password, confirmPassword } = req.body;
    
    if (password !== confirmPassword) {
      return res.status(400).json({
        success: false,
        message: 'Passwords do not match',
        errors: [{ field: 'confirmPassword', message: 'Confirm password must match the password' }]
      });
    }
    
    next();
  },

  // Validate OTP format
  otpFormat: (req, res, next) => {
    const { otp } = req.body;
    
    if (!otp || !/^\d{6}$/.test(otp)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid OTP format',
        errors: [{ field: 'otp', message: 'OTP must be a 6-digit number' }]
      });
    }
    
    next();
  }
};

// Barber registration validation schemas

// Step 1: Personal Information
const barberStep1Schema = Joi.object({
  fullName: Joi.string()
    .min(2)
    .max(100)
    .trim()
    .required()
    .pattern(/^[a-zA-Z\s]+$/)
    .messages({
      'string.empty': 'Full name is required',
      'string.min': 'Full name must be at least 2 characters long',
      'string.max': 'Full name cannot exceed 100 characters',
      'string.pattern.base': 'Full name can only contain letters and spaces',
      'any.required': 'Full name is required'
    }),

  email: Joi.string()
    .email({ tlds: { allow: false } })
    .lowercase()
    .trim()
    .required()
    .messages({
      'string.empty': 'Email is required',
      'string.email': 'Please provide a valid email address',
      'any.required': 'Email is required'
    }),

  phoneNumber: Joi.string()
    .trim()
    .required()
    .pattern(/^(\+234|234|0)?[789][01]\d{8}$/)
    .messages({
      'string.empty': 'Phone number is required',
      'string.pattern.base': 'Please provide a valid Nigerian phone number',
      'any.required': 'Phone number is required'
    }),

  address: Joi.string()
    .min(10)
    .max(500)
    .trim()
    .required()
    .messages({
      'string.empty': 'Address is required',
      'string.min': 'Address must be at least 10 characters long',
      'string.max': 'Address cannot exceed 500 characters',
      'any.required': 'Address is required'
    })
});

// Step 2: Business Information (text fields only, files handled by multer)
const barberStep2Schema = Joi.object({
  barberId: Joi.string()
    .required()
    .messages({
      'string.empty': 'Barber ID is required',
      'any.required': 'Barber ID is required'
    }),

  businessName: Joi.string()
    .min(3)
    .max(100)
    .trim()
    .required()
    .messages({
      'string.empty': 'Business name is required',
      'string.min': 'Business name must be at least 3 characters long',
      'string.max': 'Business name cannot exceed 100 characters',
      'any.required': 'Business name is required'
    })
});

// Step 3: Security Setup
const barberStep3Schema = Joi.object({
  barberId: Joi.string()
    .required()
    .messages({
      'string.empty': 'Barber ID is required',
      'any.required': 'Barber ID is required'
    }),

  password: Joi.string()
    .min(8)
    .required()
    .pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$/)
    .messages({
      'string.empty': 'Password is required',
      'string.min': 'Password must be at least 8 characters long',
      'string.pattern.base': 'Password must contain at least one uppercase letter, one lowercase letter, and one number',
      'any.required': 'Password is required'
    }),

  confirmPassword: Joi.string()
    .required()
    .valid(Joi.ref('password'))
    .messages({
      'string.empty': 'Confirm password is required',
      'any.only': 'Passwords do not match',
      'any.required': 'Confirm password is required'
    })
});

// Barber OTP verification schema
const barberOTPVerificationSchema = Joi.object({
  barberId: Joi.string()
    .required()
    .messages({
      'string.empty': 'Barber ID is required',
      'any.required': 'Barber ID is required'
    }),

  otp: Joi.string()
    .length(6)
    .pattern(/^\d{6}$/)
    .required()
    .messages({
      'string.empty': 'OTP is required',
      'string.length': 'OTP must be exactly 6 digits',
      'string.pattern.base': 'OTP must contain only numbers',
      'any.required': 'OTP is required'
    })
});

// Password reset request validation schema
const passwordResetRequestSchema = Joi.object({
  email: Joi.string()
    .email({ tlds: { allow: false } })
    .lowercase()
    .trim()
    .required()
    .messages({
      'string.empty': 'Email is required',
      'string.email': 'Please provide a valid email address',
      'any.required': 'Email is required'
    })
});

// Password reset completion validation schema
const passwordResetCompleteSchema = Joi.object({
  token: Joi.string()
    .required()
    .messages({
      'string.empty': 'Reset token is required',
      'any.required': 'Reset token is required'
    }),

  newPassword: Joi.string()
    .min(8)
    .max(128)
    .pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$/)
    .required()
    .messages({
      'string.empty': 'New password is required',
      'string.min': 'Password must be at least 8 characters long',
      'string.max': 'Password cannot exceed 128 characters',
      'string.pattern.base': 'Password must contain at least one uppercase letter, one lowercase letter, and one number',
      'any.required': 'New password is required'
    }),

  confirmPassword: Joi.string()
    .valid(Joi.ref('newPassword'))
    .required()
    .messages({
      'any.only': 'Confirm password must match the new password',
      'any.required': 'Confirm password is required'
    })
});

// Barber OTP resend schema
const barberOTPResendSchema = Joi.alternatives().try(
  Joi.object({
    email: Joi.string()
      .email({ tlds: { allow: false } })
      .lowercase()
      .trim()
      .required()
      .messages({
        'string.empty': 'Email is required',
        'string.email': 'Please provide a valid email address',
        'any.required': 'Email is required'
      })
  }),
  Joi.object({
    barberId: Joi.string()
      .required()
      .messages({
        'string.empty': 'Barber ID is required',
        'any.required': 'Barber ID is required'
      })
  })
);

// ============================================
// ADMIN VALIDATION SCHEMAS
// ============================================

// Admin login validation schema
const adminLoginSchema = Joi.object({
  email: Joi.string()
    .email({ tlds: { allow: false } })
    .lowercase()
    .trim()
    .required()
    .messages({
      'string.empty': 'Email is required',
      'string.email': 'Please provide a valid email address',
      'any.required': 'Email is required'
    }),

  password: Joi.string()
    .required()
    .messages({
      'string.empty': 'Password is required',
      'any.required': 'Password is required'
    })
});

// Admin password reset request validation schema
const adminPasswordResetRequestSchema = Joi.object({
  email: Joi.string()
    .email({ tlds: { allow: false } })
    .lowercase()
    .trim()
    .required()
    .messages({
      'string.empty': 'Email is required',
      'string.email': 'Please provide a valid email address',
      'any.required': 'Email is required'
    })
});

// Admin password reset completion validation schema
const adminPasswordResetCompleteSchema = Joi.object({
  token: Joi.string()
    .required()
    .messages({
      'string.empty': 'Reset token is required',
      'any.required': 'Reset token is required'
    }),

  newPassword: Joi.string()
    .min(8)
    .pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$/)
    .required()
    .messages({
      'string.empty': 'New password is required',
      'string.min': 'Password must be at least 8 characters long',
      'string.pattern.base': 'Password must contain at least one uppercase letter, one lowercase letter, and one number',
      'any.required': 'New password is required'
    }),

  confirmPassword: Joi.string()
    .valid(Joi.ref('newPassword'))
    .required()
    .messages({
      'any.only': 'Confirm password must match the new password',
      'any.required': 'Confirm password is required'
    })
});

// Admin profile update validation schema
const adminProfileUpdateSchema = Joi.object({
  fullName: Joi.string()
    .min(2)
    .max(100)
    .trim()
    .required()
    .pattern(/^[a-zA-Z\s]+$/)
    .messages({
      'string.empty': 'Full name is required',
      'string.min': 'Full name must be at least 2 characters long',
      'string.max': 'Full name cannot exceed 100 characters',
      'string.pattern.base': 'Full name can only contain letters and spaces',
      'any.required': 'Full name is required'
    }),

  email: Joi.string()
    .email({ tlds: { allow: false } })
    .lowercase()
    .trim()
    .required()
    .messages({
      'string.empty': 'Email is required',
      'string.email': 'Please provide a valid email address',
      'any.required': 'Email is required'
    }),

  phoneNumber: Joi.string()
    .pattern(/^\+?[1-9]\d{1,14}$/)
    .optional()
    .allow('')
    .messages({
      'string.pattern.base': 'Please provide a valid phone number'
    })
});

// Admin creation validation schema (for future admin panel use)
const adminCreationSchema = Joi.object({
  fullName: Joi.string()
    .min(2)
    .max(100)
    .trim()
    .required()
    .pattern(/^[a-zA-Z\s]+$/)
    .messages({
      'string.empty': 'Full name is required',
      'string.min': 'Full name must be at least 2 characters long',
      'string.max': 'Full name cannot exceed 100 characters',
      'string.pattern.base': 'Full name can only contain letters and spaces',
      'any.required': 'Full name is required'
    }),

  email: Joi.string()
    .email({ tlds: { allow: false } })
    .lowercase()
    .trim()
    .required()
    .messages({
      'string.empty': 'Email is required',
      'string.email': 'Please provide a valid email address',
      'any.required': 'Email is required'
    }),

  password: Joi.string()
    .min(8)
    .pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$/)
    .required()
    .messages({
      'string.empty': 'Password is required',
      'string.min': 'Password must be at least 8 characters long',
      'string.pattern.base': 'Password must contain at least one uppercase letter, one lowercase letter, and one number',
      'any.required': 'Password is required'
    }),

  role: Joi.string()
    .valid('admin', 'super_admin')
    .default('admin')
    .messages({
      'any.only': 'Role must be either admin or super_admin'
    }),

  permissions: Joi.object({
    users: Joi.object({
      view: Joi.boolean().default(true),
      create: Joi.boolean().default(false),
      edit: Joi.boolean().default(true),
      delete: Joi.boolean().default(false),
      suspend: Joi.boolean().default(true)
    }),
    barbers: Joi.object({
      view: Joi.boolean().default(true),
      approve: Joi.boolean().default(true),
      reject: Joi.boolean().default(true),
      suspend: Joi.boolean().default(true),
      edit: Joi.boolean().default(true)
    }),
    payments: Joi.object({
      view: Joi.boolean().default(true),
      process: Joi.boolean().default(true),
      refund: Joi.boolean().default(false)
    }),
    system: Joi.object({
      settings: Joi.boolean().default(false),
      logs: Joi.boolean().default(true),
      reports: Joi.boolean().default(true)
    })
  }).optional()
});

// Payment validation
const validatePaymentMethod = (paymentMethod) => {
  const validPaymentMethods = ['CARD', 'BANK_TRANSFER', 'USSD'];
  return validPaymentMethods.includes(paymentMethod.toUpperCase());
};

// Booking validation schema
const bookingSchema = Joi.object({
  barberId: Joi.string()
    .pattern(/^[0-9a-fA-F]{24}$/)
    .required()
    .messages({
      'string.empty': 'Barber ID is required',
      'string.pattern.base': 'Invalid barber ID format',
      'any.required': 'Barber ID is required'
    }),

  serviceId: Joi.string()
    .pattern(/^[0-9a-fA-F]{24}$/)
    .required()
    .messages({
      'string.empty': 'Service ID is required',
      'string.pattern.base': 'Invalid service ID format',
      'any.required': 'Service ID is required'
    }),

  date: Joi.alternatives()
    .try(
      Joi.date(),
      Joi.string().isoDate()
    )
    .required()
    .messages({
      'alternatives.match': 'Invalid date format',
      'any.required': 'Booking date is required'
    }),

  startTime: Joi.string()
    .pattern(/^([0-1][0-9]|2[0-3]):[0-5][0-9]$/)
    .required()
    .messages({
      'string.empty': 'Start time is required',
      'string.pattern.base': 'Invalid time format (HH:mm)',
      'any.required': 'Start time is required'
    }),
    
  clientType: Joi.string()
    .valid('adult', 'kid', 'mixed')
    .default('adult')
    .messages({
      'string.base': 'Client type must be a string',
      'any.only': 'Client type must be either "adult", "kid", or "mixed"'
    }),

  numAdults: Joi.number()
    .integer()
    .min(0)
    .default(1)
    .messages({
      'number.base': 'Number of adults must be a number',
      'number.integer': 'Number of adults must be a whole number',
      'number.min': 'Number of adults cannot be negative'
    }),

  numKids: Joi.number()
    .integer()
    .min(0)
    .default(0)
    .messages({
      'number.base': 'Number of kids must be a number',
      'number.integer': 'Number of kids must be a whole number',
      'number.min': 'Number of kids cannot be negative'
    }),

  totalPrice: Joi.number()
    .min(0)
    .required()
    .messages({
      'number.base': 'Total price must be a number',
      'number.min': 'Total price cannot be negative',
      'any.required': 'Total price is required'
    }),

  paymentMethod: Joi.string()
    .valid('monnify', 'other')
    .required()
    .messages({
      'string.base': 'Payment method must be a string',
      'any.only': 'Payment method must be either "monnify" or "other"',
      'any.required': 'Payment method is required'
    }),

  serviceType: Joi.string()
    .valid('home', 'shop')
    .required()
    .messages({
      'string.empty': 'Service type is required',
      'any.only': 'Service type must be either "home" or "shop"',
      'any.required': 'Service type is required'
    }),

  phoneNumber: Joi.string()
    .trim()
    .min(10)
    .max(15)
    .pattern(/^[0-9]+$/)
    .when('serviceType', {
      is: 'home',
      then: Joi.required(),
      otherwise: Joi.optional()
    })
    .messages({
      'string.empty': 'Phone number is required for home service',
      'string.min': 'Phone number must be at least 10 digits long',
      'string.max': 'Phone number cannot exceed 15 digits',
      'string.pattern.base': 'Phone number can only contain digits',
      'any.required': 'Phone number is required for home service'
    }),

  address: Joi.string()
    .trim()
    .min(10)
    .max(500)
    .when('serviceType', {
      is: 'home',
      then: Joi.required(),
      otherwise: Joi.optional()
    })
    .messages({
      'string.empty': 'Address is required for home service',
      'string.min': 'Address must be at least 10 characters long',
      'string.max': 'Address cannot exceed 500 characters',
      'any.required': 'Address is required for home service'
    })
}).custom((value, helpers) => {
  // Validate that total number of people is greater than 0
  const totalPeople = (value.numAdults || 0) + (value.numKids || 0);
  if (totalPeople === 0) {
    return helpers.error('custom.totalPeople');
  }
  return value;
}).messages({
  'custom.totalPeople': 'At least one person must be specified for the booking'
});

// Validate booking middleware
const validateBooking = validate(bookingSchema);

module.exports = {
  validate,
  validateOTPAttempts,
  userRegistrationSchema,
  loginSchema,
  otpVerificationSchema,
  otpResendSchema,
  validateBooking,
  isValidEmail,
  isStrongPassword,
  sanitizeInput,
  customValidation,
  // Barber validation schemas
  barberStep1Schema,
  barberStep2Schema,
  barberStep3Schema,
  barberOTPVerificationSchema,
  barberOTPResendSchema,
  // Password reset validation schemas
  passwordResetRequestSchema,
  passwordResetCompleteSchema,
  // Admin validation schemas
  adminLoginSchema,
  adminPasswordResetRequestSchema,
  adminPasswordResetCompleteSchema,
  adminProfileUpdateSchema,
  adminCreationSchema,
  validatePaymentMethod
};
