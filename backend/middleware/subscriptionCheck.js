const Subscription = require('../models/Subscription');
const Barber = require('../models/Barber');
const { AppError } = require('../utils/errorHandlers');

// Middleware to check if barber has active subscription for profile visibility
const checkSubscriptionForProfileVisibility = async (req, res, next) => {
  try {
    if (req.user && req.user.role === 'barber') {
      const subscription = await Subscription.getOrCreate(req.user._id);
      
      // If subscription is not active, ensure profile is not visible
      if (!subscription.isActive) {
        const barber = await Barber.findById(req.user._id);
        if (barber && barber.isProfileActive) {
          console.log(`Auto-disabling profile for barber ${barber.fullName} (${barber.email}) - Subscription status: ${subscription.status}`);
          barber.isProfileActive = false;
          await barber.save();
          
          // Set a flag to inform the barber about the auto-disable
          req.profileAutoDisabled = true;
          req.subscriptionStatus = subscription.status;
        }
      }
    }
    next();
  } catch (error) {
    console.error('Error in subscription check middleware:', error);
    next(); // Continue even if check fails
  }
};

// Middleware to ensure only barbers with active subscriptions are visible to users
const filterActiveSubscriptions = async (req, res, next) => {
  try {
    // This middleware will be used in routes that fetch barbers for users
    req.subscriptionFilter = {
      // Only show barbers with active profiles AND active subscriptions
      isProfileActive: true
    };
    
    next();
  } catch (error) {
    console.error('Error in subscription filter middleware:', error);
    next();
  }
};

// Middleware to check subscription before allowing booking
const checkSubscriptionForBooking = async (req, res, next) => {
  try {
    const { barberId } = req.body || req.params;
    
    if (barberId) {
      const subscription = await Subscription.findOne({ barber: barberId });
      
      if (!subscription || !subscription.isActive) {
        return res.status(400).json({
          success: false,
          message: 'This barber is currently unavailable. Please try booking with another barber.',
          error: 'SUBSCRIPTION_INACTIVE'
        });
      }
    }
    
    next();
  } catch (error) {
    console.error('Error checking subscription for booking:', error);
    return next(new AppError('Unable to process booking at this time', 500));
  }
};

module.exports = {
  checkSubscriptionForProfileVisibility,
  filterActiveSubscriptions,
  checkSubscriptionForBooking
}; 