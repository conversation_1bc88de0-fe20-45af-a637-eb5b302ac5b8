const multer = require('multer');
const path = require('path');

// File type validation
const fileFilter = (req, file, cb) => {
  // Define allowed file types for different fields
  const allowedTypes = {
    // For passport photo - only images
    passportPhoto: {
      mimeTypes: ['image/jpeg', 'image/jpg', 'image/png'],
      extensions: ['.jpg', '.jpeg', '.png']
    },
    // For CAC certificate and NIN - images and PDFs
    cacCertificate: {
      mimeTypes: ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'],
      extensions: ['.jpg', '.jpeg', '.png', '.pdf']
    },
    ninDocument: {
      mimeTypes: ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'],
      extensions: ['.jpg', '.jpeg', '.png', '.pdf']
    },
    // For portfolio - images and videos
    portfolio: {
      mimeTypes: ['image/jpeg', 'image/jpg', 'image/png', 'video/mp4', 'video/mpeg', 'video/quicktime'],
      extensions: ['.jpg', '.jpeg', '.png', '.mp4', '.mpeg', '.mov']
    },
    // For profile picture - only images
    profilePicture: {
      mimeTypes: ['image/jpeg', 'image/jpg', 'image/png'],
      extensions: ['.jpg', '.jpeg', '.png']
    }
  };

  const fieldConfig = allowedTypes[file.fieldname];
  
  if (!fieldConfig) {
    return cb(new Error(`Unexpected field: ${file.fieldname}`), false);
  }

  // Check MIME type
  if (!fieldConfig.mimeTypes.includes(file.mimetype)) {
    return cb(new Error(`Invalid file type for ${file.fieldname}. Allowed types: ${fieldConfig.mimeTypes.join(', ')}`), false);
  }

  // Check file extension
  const fileExtension = path.extname(file.originalname).toLowerCase();
  if (!fieldConfig.extensions.includes(fileExtension)) {
    return cb(new Error(`Invalid file extension for ${file.fieldname}. Allowed extensions: ${fieldConfig.extensions.join(', ')}`), false);
  }

  cb(null, true);
};

// Configure multer for memory storage (we'll upload to Cloudinary)
const storage = multer.memoryStorage();

// File size limits (in bytes)
const limits = {
  fileSize: 10 * 1024 * 1024, // 10MB for portfolio videos, 5MB for others
  files: 10 // Maximum 10 files for portfolio
};

// Create multer instance
const upload = multer({
  storage,
  limits,
  fileFilter
});

// Middleware for barber document upload (Step 2)
const uploadBarberDocuments = upload.fields([
  { name: 'cacCertificate', maxCount: 1 },
  { name: 'ninDocument', maxCount: 1 },
  { name: 'passportPhoto', maxCount: 1 }
]);

// Middleware for portfolio upload
const uploadPortfolio = upload.array('portfolio', 10);

// Middleware for profile update (profile picture + portfolio)
const uploadProfileData = upload.fields([
  { name: 'profilePicture', maxCount: 1 },
  { name: 'portfolio', maxCount: 10 }
]);

// Error handling middleware for multer
const handleUploadError = (error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    switch (error.code) {
      case 'LIMIT_FILE_SIZE':
        return res.status(400).json({
          success: false,
          message: 'File too large. Maximum size allowed is 5MB per file.',
          errors: [{ field: 'file', message: 'File size exceeds limit' }]
        });
      case 'LIMIT_FILE_COUNT':
        return res.status(400).json({
          success: false,
          message: 'Too many files. Maximum 3 files allowed.',
          errors: [{ field: 'files', message: 'File count exceeds limit' }]
        });
      case 'LIMIT_UNEXPECTED_FILE':
        return res.status(400).json({
          success: false,
          message: 'Unexpected file field.',
          errors: [{ field: 'file', message: 'Unexpected file field' }]
        });
      default:
        return res.status(400).json({
          success: false,
          message: 'File upload error.',
          errors: [{ field: 'file', message: error.message }]
        });
    }
  }

  if (error.message.includes('Invalid file type') || error.message.includes('Invalid file extension')) {
    return res.status(400).json({
      success: false,
      message: error.message,
      errors: [{ field: 'file', message: error.message }]
    });
  }

  next(error);
};

// Validate uploaded files middleware
const validateUploadedFiles = (req, res, next) => {
  const requiredFiles = ['cacCertificate', 'ninDocument', 'passportPhoto'];
  const uploadedFiles = req.files || {};
  
  const missingFiles = requiredFiles.filter(field => !uploadedFiles[field] || uploadedFiles[field].length === 0);
  
  if (missingFiles.length > 0) {
    return res.status(400).json({
      success: false,
      message: 'Missing required documents.',
      errors: missingFiles.map(field => ({
        field,
        message: `${field} is required`
      }))
    });
  }

  // Additional validation for passport photo (image dimensions)
  const passportPhoto = uploadedFiles.passportPhoto[0];
  if (passportPhoto && passportPhoto.mimetype.startsWith('image/')) {
    // We'll validate dimensions after upload to Cloudinary
    // For now, just ensure it's an image
    if (!passportPhoto.mimetype.startsWith('image/')) {
      return res.status(400).json({
        success: false,
        message: 'Passport photo must be an image file.',
        errors: [{ field: 'passportPhoto', message: 'Must be an image file' }]
      });
    }
  }

  next();
};

module.exports = {
  uploadBarberDocuments,
  uploadPortfolio,
  uploadProfileData,
  handleUploadError,
  validateUploadedFiles,
  upload
};
