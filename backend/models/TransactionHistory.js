const mongoose = require('mongoose');
const paymentConfig = require('../config/payment');

const transactionHistorySchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  barber: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Barber',
    required: true,
    index: true
  },
  booking: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Booking',
    index: true
  },
  amount: {
    type: Number,
    required: true
  },
  transactionId: {
    type: String,
    required: true,
    unique: true
  },
  transactionReference: {
    type: String,
    required: true,
    unique: true
  },
  paymentReference: {
    type: String,
    required: true,
    unique: true
  },
  status: {
    type: String,
    enum: ['success', 'failed', 'pending', 'cancelled'],
    default: 'pending',
    index: true
  },
  paymentMethod: {
    type: String,
    enum: ['monnify', 'other'],
    default: 'monnify'
  },
  isHeldInEscrow: {
    type: Boolean,
    default: false
  },
  commissionAmount: {
    type: Number,
    default: 0
  },
  commissionPercentage: {
    type: Number,
    default: () => paymentConfig.platformCommission // Use commission from config
  },
  barberAmount: {
    type: Number,
    default: 0
  },
  paymentDetails: {
    type: mongoose.Schema.Types.Mixed
  },
  metadata: {
    type: mongoose.Schema.Types.Mixed
  }
}, {
  timestamps: true
});

// Calculate amounts based on total
transactionHistorySchema.methods.calculateAmounts = function() {
  this.commissionAmount = this.amount * (this.commissionPercentage / 100);
  this.barberAmount = this.amount - this.commissionAmount;
};

module.exports = mongoose.model('TransactionHistory', transactionHistorySchema); 