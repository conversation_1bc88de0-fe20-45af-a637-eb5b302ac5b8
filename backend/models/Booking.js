const mongoose = require('mongoose');

const bookingSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  barber: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Barber',
    required: true
  },
  service: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Service',
    required: true
  },
  serviceType: {
    type: String,
    enum: ['home', 'shop'],
    required: true
  },
  date: {
    type: Date,
    required: true
  },
  startTime: {
    type: String, // Format: "HH:mm"
    required: true,
    validate: {
      validator: function(v) {
        return /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.test(v);
      },
      message: props => `${props.value} is not a valid time format! Use HH:mm`
    }
  },
  endTime: {
    type: String, // Format: "HH:mm"
    required: true,
    validate: {
      validator: function(v) {
        return /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.test(v);
      },
      message: props => `${props.value} is not a valid time format! Use HH:mm`
    }
  },
  status: {
    type: String,
    enum: ['pending', 'confirmed', 'in_progress', 'completed', 'reviewed', 'cancelled'],
    default: 'pending'
  },
  totalPrice: {
    type: Number,
    required: true,
    min: 0
  },
  clientType: {
    type: String,
    enum: ['adult', 'kid', 'mixed'],
    default: 'adult'
  },
  numAdults: {
    type: Number,
    default: 1,
    min: 0,
    validate: {
      validator: function(v) {
        return this.numAdults + this.numKids > 0;
      },
      message: 'Total number of people must be greater than 0'
    }
  },
  numKids: {
    type: Number,
    default: 0,
    min: 0
  },
  notes: {
    type: String,
    trim: true
  },
  cancellationReason: {
    type: String,
    trim: true
  },
  paymentStatus: {
    type: String,
    enum: ['pending', 'processing', 'completed', 'refunded', 'failed', 'cancelled'],
    default: 'pending'
  },
  paymentMethod: {
    type: String,
    enum: ['monnify', 'other'],
    required: true
  },
  paymentReference: {
    type: String
  },
  transactionReference: {
    type: String
  },
  paymentDetails: {
    transactionId: String,
    paymentReference: String,
    escrowId: String,
    paymentDate: Date
  },
  phoneNumber: {
    type: String,
    required: function() {
      return this.serviceType === 'home';
    },
    validate: {
      validator: function(v) {
        if (this.serviceType === 'home') {
          return /^\+?[1-9]\d{1,14}$/.test(v);
        }
        return true;
      },
      message: props => `${props.value} is not a valid phone number!`
    }
  },
  address: {
    type: String,
    required: function() {
      return this.serviceType === 'home';
    },
    validate: {
      validator: function(v) {
        if (this.serviceType === 'home') {
          return v.length >= 10;
        }
        return true;
      },
      message: props => 'Address must be at least 10 characters long!'
    }
  },
  review: {
    rating: {
      type: Number,
      min: 1,
      max: 5
    },
    comment: String,
    createdAt: Date
  },
  notifications: [{
    type: {
      type: String,
      enum: ['booking_confirmation', 'reminder_24h', 'reminder_2h', 'status_change', 'payment_confirmation', 'review_request']
    },
    sent: {
      type: Boolean,
      default: false
    },
    sentAt: Date
  }],
  slotHoldExpiry: {
    type: Date
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Indexes for faster queries
bookingSchema.index({ user: 1, date: -1 });
bookingSchema.index({ barber: 1, date: -1 });
bookingSchema.index({ status: 1, date: 1 });
bookingSchema.index({ slotHoldExpiry: 1 }, { expireAfterSeconds: 0 });

// Validate that endTime is after startTime
bookingSchema.pre('save', function(next) {
  const startHour = parseInt(this.startTime.split(':')[0]);
  const startMinute = parseInt(this.startTime.split(':')[1]);
  const endHour = parseInt(this.endTime.split(':')[0]);
  const endMinute = parseInt(this.endTime.split(':')[1]);

  if (endHour < startHour || (endHour === startHour && endMinute <= startMinute)) {
    next(new Error('End time must be after start time'));
  }
  next();
});

// Validate that booking date is not in the past (only for new bookings)
bookingSchema.pre('save', function(next) {
  // Skip validation for existing bookings (when updating status, adding reviews, etc.)
  if (!this.isNew && this.date < new Date()) {
    // Allow past date for existing bookings being updated
    return next();
  }
  
  // Only validate date for new bookings
  if (this.isNew && this.date < new Date()) {
    return next(new Error('Booking date cannot be in the past'));
  }
  
  next();
});

// Status transition validation
bookingSchema.pre('save', function(next) {
  if (this.isModified('status')) {
    const validTransitions = {
      pending: ['confirmed', 'cancelled'],
      confirmed: ['in_progress', 'cancelled'],
      in_progress: ['completed', 'cancelled'],
      completed: ['reviewed'],
      reviewed: [],
      cancelled: []
    };

    if (this.isNew) {
      if (this.status !== 'pending') {
        return next(new Error('New bookings must start with pending status'));
      }
    } else {
      const oldStatus = this._oldStatus || this.status;
      
      // Allow same status (for cases like adding reviews to completed bookings)
      if (oldStatus === this.status) {
        return next();
      }
      
      if (!validTransitions[oldStatus].includes(this.status)) {
        return next(new Error(`Invalid status transition from ${oldStatus} to ${this.status}`));
      }
    }
  }
  this._oldStatus = this.status;
  next();
});

// Update timestamps on save
bookingSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

const Booking = mongoose.model('Booking', bookingSchema);

module.exports = Booking; 