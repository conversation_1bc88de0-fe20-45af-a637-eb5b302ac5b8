const mongoose = require('mongoose');
const { v4: uuidv4 } = require('uuid');

const passwordResetSchema = new mongoose.Schema({
  email: {
    type: String,
    required: [true, 'Email is required'],
    lowercase: true,
    trim: true,
    match: [
      /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
      'Please provide a valid email address'
    ]
  },
  token: {
    type: String,
    required: [true, 'Reset token is required']
  },
  userType: {
    type: String,
    enum: ['user', 'barber', 'admin'],
    required: [true, 'User type is required']
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    required: [true, 'User ID is required']
  },
  expiresAt: {
    type: Date,
    required: [true, 'Expiry date is required'],
    default: () => new Date(Date.now() + 10 * 60 * 1000) // 10 minutes from now
  },
  used: {
    type: Boolean,
    default: false
  },
  usedAt: {
    type: Date,
    default: null
  },
  ipAddress: {
    type: String,
    default: null
  },
  userAgent: {
    type: String,
    default: null
  }
}, {
  timestamps: true
});

// Index for performance and automatic cleanup
passwordResetSchema.index({ email: 1 });
passwordResetSchema.index({ token: 1 }, { unique: true });
passwordResetSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 }); // Auto-delete expired documents
passwordResetSchema.index({ userId: 1 });
passwordResetSchema.index({ userType: 1 });

// Static method to generate reset token
passwordResetSchema.statics.generateResetToken = function(email, userId, userType, ipAddress = null, userAgent = null) {
  const token = uuidv4() + '-' + Date.now(); // UUID + timestamp for uniqueness
  
  return new this({
    email,
    token,
    userId,
    userType,
    ipAddress,
    userAgent,
    expiresAt: new Date(Date.now() + 10 * 60 * 1000) // 10 minutes
  });
};

// Instance method to check if token is valid
passwordResetSchema.methods.isValid = function() {
  return !this.used && this.expiresAt > new Date();
};

// Instance method to mark token as used
passwordResetSchema.methods.markAsUsed = function() {
  this.used = true;
  this.usedAt = new Date();
  return this.save();
};

// Static method to find valid token
passwordResetSchema.statics.findValidToken = function(token) {
  return this.findOne({
    token,
    used: false,
    expiresAt: { $gt: new Date() }
  });
};

// Static method to invalidate all tokens for a user
passwordResetSchema.statics.invalidateUserTokens = function(userId, userType) {
  return this.updateMany(
    { userId, userType, used: false },
    { 
      $set: { 
        used: true, 
        usedAt: new Date() 
      } 
    }
  );
};

// Pre-save middleware to ensure token uniqueness
passwordResetSchema.pre('save', async function(next) {
  if (this.isNew) {
    // Check if token already exists (very unlikely with UUID + timestamp)
    const existingToken = await this.constructor.findOne({ token: this.token });
    if (existingToken) {
      // Generate new token if collision occurs
      this.token = uuidv4() + '-' + Date.now();
    }
  }
  next();
});

module.exports = mongoose.model('PasswordReset', passwordResetSchema);
