const mongoose = require('mongoose');

const disputeSchema = new mongoose.Schema({
  payment: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Payment',
    required: true
  },
  booking: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Booking',
    required: true
  },
  initiator: {
    type: mongoose.Schema.Types.ObjectId,
    refPath: 'initiatorType',
    required: true
  },
  initiatorType: {
    type: String,
    required: true,
    enum: ['User', 'Barber']
  },
  reason: {
    type: String,
    required: true,
    enum: [
      'BARBER_CANCELLATION',
      'USER_CANCELLATION',
      'SERVICE_NOT_PROVIDED',
      'QUALITY_ISSUE',
      'TECHNICAL_ISSUE',
      'OTHER'
    ]
  },
  description: {
    type: String,
    required: true
  },
  evidence: [{
    type: String, // URLs to uploaded evidence files
    required: false
  }],
  status: {
    type: String,
    enum: ['PENDING', 'UNDER_REVIEW', 'RESOLVED', 'CLOSED'],
    default: 'PENDING'
  },
  resolution: {
    type: {
      decision: {
        type: String,
        enum: ['FULL_REFUND', 'PARTIAL_REFUND', 'NO_REFUND', 'RELEASE_PAYMENT'],
        required: false
      },
      refundAmount: {
        type: Number,
        required: false
      },
      notes: {
        type: String,
        required: false
      }
    },
    required: false
  },
  adminNotes: [{
    note: String,
    addedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Admin'
    },
    addedAt: {
      type: Date,
      default: Date.now
    }
  }],
  resolvedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin',
    required: false
  },
  resolvedAt: {
    type: Date,
    required: false
  },
  deadlineDate: {
    type: Date,
    required: true
  }
}, {
  timestamps: true
});

// Set deadline to 5 business days from creation
disputeSchema.pre('save', function(next) {
  if (this.isNew) {
    const deadline = new Date();
    deadline.setDate(deadline.getDate() + 5); // Add 5 days
    this.deadlineDate = deadline;
  }
  next();
});

// Index for better query performance
disputeSchema.index({ status: 1 });
disputeSchema.index({ payment: 1 });
disputeSchema.index({ booking: 1 });
disputeSchema.index({ deadlineDate: 1 });

const Dispute = mongoose.model('Dispute', disputeSchema);

module.exports = Dispute; 