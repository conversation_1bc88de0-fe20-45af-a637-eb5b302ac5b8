const mongoose = require('mongoose');

const serviceSchema = new mongoose.Schema({
  barber: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Barber',
    required: true
  },
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    required: true
  },
  duration: {
    type: Number, // Duration in minutes
    required: true,
    min: 5
  },
  category: {
    type: String,
    required: true,
    enum: ['Haircuts', 'Beard Services', 'Hair Coloring', 'Hair Line Shaping']
  },
  priceAdult: {
    type: Number,
    required: true,
    min: 0
  },
  priceKid: {
    type: Number,
    required: false,
    min: 0,
    default: 0
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Index for faster queries
serviceSchema.index({ barber: 1, category: 1 });
serviceSchema.index({ priceAdult: 1 });

const Service = mongoose.model('Service', serviceSchema);

module.exports = Service; 