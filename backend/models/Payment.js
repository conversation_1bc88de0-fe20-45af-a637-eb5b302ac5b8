const mongoose = require('mongoose');
const paymentConfig = require('../config/payment');

const paymentSchema = new mongoose.Schema({
  booking: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Booking',
    required: true
  },
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  barber: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Barber',
    required: true
  },
  amount: {
    type: Number,
    required: true
  },
  platformCommission: {
    type: Number,
    required: true
  },
  barberAmount: {
    type: Number,
    required: true
  },
  status: {
    type: String,
    enum: ['pending', 'held', 'released', 'paid', 'refunded', 'disputed', 'failed', 'cancelled'],
    default: 'pending',
    index: true
  },
  paymentMethod: {
    type: String,
    enum: ['monnify', 'other'],
    default: 'monnify'
  },
  transactionReference: {
    type: String,
    required: true,
    unique: true
  },
  paymentReference: {
    type: String,
    required: true,
    unique: true
  },
  checkoutUrl: {
    type: String
  },
  releaseEligibleAt: {
    type: Date,
    index: true
  },
  autoReleaseAt: {
    type: Date,
    index: true
  },
  releasedAt: {
    type: Date
  },
  paidAt: {
    type: Date
  },
  webhookData: {
    type: mongoose.Schema.Types.Mixed
  },
  metadata: {
    serviceCompleted: {
      type: Boolean,
      default: false
    },
    userConfirmed: {
      type: Boolean,
      default: false
    },
    userRated: {
      type: Boolean,
      default: false
    },
    barberRated: {
      type: Boolean,
      default: false
    }
  }
}, {
  timestamps: true
});

// Calculate platform commission and barber amount
paymentSchema.methods.calculateAmounts = function(totalAmount) {
  const commissionRate = paymentConfig.platformCommission / 100; // Convert percentage to decimal
  
  this.amount = totalAmount;
  this.platformCommission = totalAmount * commissionRate;
  this.barberAmount = totalAmount - this.platformCommission;
};

// Additional indexes for better query performance
paymentSchema.index({ booking: 1 });
paymentSchema.index({ user: 1 });
paymentSchema.index({ barber: 1 });

// Method to check if payment is eligible for release
paymentSchema.methods.isEligibleForRelease = function() {
  const { serviceCompleted, userConfirmed } = this.metadata;
  return serviceCompleted && userConfirmed;
};

module.exports = mongoose.model('Payment', paymentSchema); 