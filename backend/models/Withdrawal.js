const mongoose = require('mongoose');

const withdrawalSchema = new mongoose.Schema({
  barber: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Barber',
    required: true
  },
  amount: {
    type: Number,
    required: true,
    min: 5000 // Minimum withdrawal amount ₦5,000
  },
  status: {
    type: String,
    enum: ['PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'CANCELLED'],
    default: 'PENDING'
  },
  bankDetails: {
    bankName: {
      type: String,
      required: true
    },
    bankCode: {
      type: String,
      required: true
    },
    accountNumber: {
      type: String,
      required: true,
      validate: {
        validator: function(v) {
          return /^\d{10}$/.test(v); // 10 digits validation
        },
        message: 'Account number must be 10 digits'
      }
    },
    accountName: {
      type: String,
      required: true
    }
  },
  withdrawalReference: {
    type: String,
    required: true,
    unique: true
  },
  processedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin',
    required: false
  },
  processedAt: {
    type: Date
  },
  notes: {
    type: String
  },
  failureReason: {
    type: String
  },
  metadata: {
    dailyWithdrawalTotal: Number,
    monthlyWithdrawalTotal: Number,
    transactionId: String,
    gatewayResponse: Object
  }
}, {
  timestamps: true
});

// Generate unique withdrawal reference
withdrawalSchema.pre('save', function(next) {
  if (this.isNew) {
    this.withdrawalReference = `WTH-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
  next();
});

// Indexes for better query performance
withdrawalSchema.index({ barber: 1 });
withdrawalSchema.index({ status: 1 });
withdrawalSchema.index({ createdAt: 1 });

// Method to validate withdrawal limits
withdrawalSchema.methods.validateWithdrawalLimits = async function() {
  const today = new Date();
  const startOfDay = new Date(today.setHours(0, 0, 0, 0));
  const startOfMonth = new Date(today.setDate(1));

  // Get daily total
  const dailyTotal = await this.constructor.aggregate([
    {
      $match: {
        barber: this.barber,
        createdAt: { $gte: startOfDay },
        status: { $in: ['PENDING', 'PROCESSING', 'COMPLETED'] }
      }
    },
    {
      $group: {
        _id: null,
        total: { $sum: '$amount' }
      }
    }
  ]);

  // Get monthly total
  const monthlyTotal = await this.constructor.aggregate([
    {
      $match: {
        barber: this.barber,
        createdAt: { $gte: startOfMonth },
        status: { $in: ['PENDING', 'PROCESSING', 'COMPLETED'] }
      }
    },
    {
      $group: {
        _id: null,
        total: { $sum: '$amount' }
      }
    }
  ]);

  const dailyAmount = (dailyTotal[0]?.total || 0) + this.amount;
  const monthlyAmount = (monthlyTotal[0]?.total || 0) + this.amount;

  if (dailyAmount > 1000000) { // ₦1,000,000 daily limit
    throw new Error('Daily withdrawal limit exceeded');
  }

  if (monthlyAmount > 5000000) { // ₦5,000,000 monthly limit
    throw new Error('Monthly withdrawal limit exceeded');
  }

  // Store the totals in metadata
  this.metadata = {
    ...this.metadata,
    dailyWithdrawalTotal: dailyAmount,
    monthlyWithdrawalTotal: monthlyAmount
  };
};

const Withdrawal = mongoose.model('Withdrawal', withdrawalSchema);

module.exports = Withdrawal; 