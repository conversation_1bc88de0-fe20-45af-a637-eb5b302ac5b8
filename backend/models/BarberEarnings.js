const mongoose = require('mongoose');

const barberEarningsSchema = new mongoose.Schema({
  barber: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Barber',
    required: true,
    unique: true
  },
  availableBalance: {
    type: Number,
    required: true,
    default: 0,
    min: 0
  },
  pendingBalance: {
    type: Number,
    required: true,
    default: 0,
    min: 0
  },
  totalEarnings: {
    type: Number,
    required: true,
    default: 0,
    min: 0
  },
  totalWithdrawn: {
    type: Number,
    required: true,
    default: 0,
    min: 0
  },
  lastWithdrawalDate: {
    type: Date
  },
  transactions: [{
    type: {
      type: String,
      enum: ['SERVICE_PAYMENT', 'COMMISSION_DEDUCTION', 'WITHDRAWAL', 'REFUND', 'DISPUTE_RESOLUTION'],
      required: true
    },
    amount: {
      type: Number,
      required: true
    },
    reference: {
      type: String,
      required: true
    },
    description: String,
    relatedBooking: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Booking'
    },
    relatedPayment: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Payment'
    },
    relatedWithdrawal: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Withdrawal'
    },
    balanceAfter: {
      available: Number,
      pending: Number
    },
    createdAt: {
      type: Date,
      default: Date.now
    }
  }]
}, {
  timestamps: true
});

// Indexes for better query performance
barberEarningsSchema.index({ 'transactions.type': 1 });
barberEarningsSchema.index({ 'transactions.createdAt': 1 });

// Method to add a new transaction
barberEarningsSchema.methods.addTransaction = function(transactionData) {
  this.transactions.push({
    ...transactionData,
    balanceAfter: {
      available: this.availableBalance,
      pending: this.pendingBalance
    }
  });
};

// Method to update balances based on service completion
barberEarningsSchema.methods.handleServiceCompletion = async function(payment) {
  const transaction = {
    type: 'SERVICE_PAYMENT',
    amount: payment.barberAmount,
    reference: payment.transactionReference,
    description: 'Service payment received',
    relatedBooking: payment.booking,
    relatedPayment: payment._id
  };

  this.pendingBalance += payment.barberAmount;
  this.totalEarnings += payment.barberAmount;
  
  this.addTransaction(transaction);

  const commissionTransaction = {
    type: 'COMMISSION_DEDUCTION',
    amount: -payment.platformCommission,
    reference: `COM-${payment.transactionReference}`,
    description: 'Platform commission deducted',
    relatedBooking: payment.booking,
    relatedPayment: payment._id
  };

  this.addTransaction(commissionTransaction);
  
  await this.save();
};

// Method to handle withdrawal
barberEarningsSchema.methods.handleWithdrawal = async function(withdrawal) {
  if (withdrawal.amount > this.availableBalance) {
    throw new Error('Insufficient available balance');
  }

  this.availableBalance -= withdrawal.amount;
  this.totalWithdrawn += withdrawal.amount;
  this.lastWithdrawalDate = new Date();

  const transaction = {
    type: 'WITHDRAWAL',
    amount: -withdrawal.amount,
    reference: withdrawal.withdrawalReference,
    description: 'Withdrawal processed',
    relatedWithdrawal: withdrawal._id
  };

  this.addTransaction(transaction);
  
  await this.save();
};

// Method to release pending balance to available balance
barberEarningsSchema.methods.releasePendingBalance = async function(payment) {
  const amount = payment.barberAmount;
  
  console.log(`Releasing pending balance: ${amount}, Current pending: ${this.pendingBalance}`);
  
  if (amount > this.pendingBalance) {
    console.log(`Insufficient pending balance. Amount: ${amount}, Pending: ${this.pendingBalance}`);
    
    // If pending balance is insufficient but payment exists, credit directly to available balance
    // This handles cases where the payment flow was interrupted
    console.log('Adding amount directly to available balance due to insufficient pending balance');
    this.availableBalance += amount;
    this.totalEarnings += amount;
    
    // Add transaction record
    this.addTransaction({
      type: 'SERVICE_PAYMENT',
      amount: amount,
      reference: payment.paymentReference || payment.transactionReference,
      description: `Direct service payment credit - ${payment.amount} less commission`,
      relatedPayment: payment._id
    });
  } else {
    // Normal flow: deduct from pending and add to available
    this.pendingBalance -= amount;
    this.availableBalance += amount;
  }
  
  await this.save();
};

// Static method to get barber earnings or create if not exists
barberEarningsSchema.statics.getOrCreate = async function(barberId) {
  let earnings = await this.findOne({ barber: barberId });
  
  if (!earnings) {
    earnings = new this({
      barber: barberId
    });
    await earnings.save();
  }
  
  return earnings;
};

const BarberEarnings = mongoose.model('BarberEarnings', barberEarningsSchema);

module.exports = BarberEarnings; 