const mongoose = require('mongoose');

const verificationRequestSchema = new mongoose.Schema({
  barberId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Barber',
    required: true,
    unique: true
  },
  requestType: {
    type: String,
    enum: ['initial_verification', 'document_update', 'profile_update'],
    default: 'initial_verification'
  },
  status: {
    type: String,
    enum: ['pending', 'in_review', 'approved', 'rejected', 'expired'],
    default: 'pending'
  },
  priority: {
    type: String,
    enum: ['low', 'normal', 'high', 'urgent'],
    default: 'normal'
  },
  
  // Assignment details
  assignedTo: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User', // Admin user
    default: null
  },
  assignedAt: {
    type: Date,
    default: null
  },
  
  // Review details
  reviewStartedAt: {
    type: Date,
    default: null
  },
  reviewCompletedAt: {
    type: Date,
    default: null
  },
  reviewNotes: {
    type: String,
    trim: true
  },
  
  // Decision details
  decision: {
    type: String,
    enum: ['approved', 'rejected', 'needs_clarification'],
    default: null
  },
  approvalMessage: {
    type: String,
    trim: true
  },
  rejectionReason: {
    type: String,
    trim: true
  },
  clarificationRequested: {
    type: String,
    trim: true
  },
  
  // SLA Management
  slaDeadline: {
    type: Date,
    required: true
  },
  remindersSent: {
    type: Number,
    default: 0
  },
  lastReminderSent: {
    type: Date,
    default: null
  },
  
  // Document verification details
  documentsVerified: {
    cacCertificate: {
      verified: { type: Boolean, default: false },
      notes: String,
      verifiedAt: Date,
      verifiedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      }
    },
    ninDocument: {
      verified: { type: Boolean, default: false },
      notes: String,
      verifiedAt: Date,
      verifiedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      }
    },
    passportPhoto: {
      verified: { type: Boolean, default: false },
      notes: String,
      verifiedAt: Date,
      verifiedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      }
    }
  },
  
  // Audit trail
  auditTrail: [{
    action: {
      type: String,
      required: true
    },
    performedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    performedAt: {
      type: Date,
      default: Date.now
    },
    details: {
      type: mongoose.Schema.Types.Mixed
    },
    ipAddress: String,
    userAgent: String
  }],
  
  // Communication log
  communications: [{
    type: {
      type: String,
      enum: ['email_sent', 'sms_sent', 'notification_sent', 'call_made'],
      required: true
    },
    recipient: {
      type: String,
      required: true
    },
    subject: String,
    content: String,
    sentAt: {
      type: Date,
      default: Date.now
    },
    sentBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    status: {
      type: String,
      enum: ['sent', 'delivered', 'failed', 'bounced'],
      default: 'sent'
    }
  }]
}, {
  timestamps: true
});

// Indexes for performance
// Note: barberId unique index is already created by the schema field definition
verificationRequestSchema.index({ status: 1 });
verificationRequestSchema.index({ assignedTo: 1 });
verificationRequestSchema.index({ slaDeadline: 1 });
verificationRequestSchema.index({ createdAt: 1 });
verificationRequestSchema.index({ priority: 1, createdAt: 1 });

// Pre-save middleware to set SLA deadline
verificationRequestSchema.pre('save', function(next) {
  // Set SLA deadline to 48 hours from creation if not already set
  if (this.isNew && !this.slaDeadline) {
    this.slaDeadline = new Date(Date.now() + 48 * 60 * 60 * 1000); // 48 hours
  }
  next();
});

// Instance method to add audit trail entry
verificationRequestSchema.methods.addAuditEntry = function(action, performedBy, details = {}, ipAddress = null, userAgent = null) {
  this.auditTrail.push({
    action,
    performedBy,
    details,
    ipAddress,
    userAgent
  });
};

// Instance method to add communication log entry
verificationRequestSchema.methods.addCommunication = function(type, recipient, subject, content, sentBy = null) {
  this.communications.push({
    type,
    recipient,
    subject,
    content,
    sentBy
  });
};

// Instance method to check if SLA is breached
verificationRequestSchema.methods.isSLABreached = function() {
  return new Date() > this.slaDeadline && this.status === 'pending';
};

// Instance method to calculate time remaining for SLA
verificationRequestSchema.methods.getTimeRemainingForSLA = function() {
  const now = new Date();
  const timeRemaining = this.slaDeadline - now;
  
  if (timeRemaining <= 0) {
    return { expired: true, timeRemaining: 0 };
  }
  
  const hours = Math.floor(timeRemaining / (1000 * 60 * 60));
  const minutes = Math.floor((timeRemaining % (1000 * 60 * 60)) / (1000 * 60));
  
  return {
    expired: false,
    timeRemaining,
    hours,
    minutes,
    formatted: `${hours}h ${minutes}m`
  };
};

// Static method to get pending requests for assignment
verificationRequestSchema.statics.getPendingForAssignment = function() {
  return this.find({
    status: 'pending',
    assignedTo: null
  }).sort({ priority: -1, createdAt: 1 });
};

// Static method to get overdue requests
verificationRequestSchema.statics.getOverdueRequests = function() {
  return this.find({
    status: { $in: ['pending', 'in_review'] },
    slaDeadline: { $lt: new Date() }
  }).populate('barberId assignedTo');
};

// Virtual for request age
verificationRequestSchema.virtual('ageInHours').get(function() {
  const now = new Date();
  const ageInMs = now - this.createdAt;
  return Math.floor(ageInMs / (1000 * 60 * 60));
});

// Ensure virtual fields are serialized
verificationRequestSchema.set('toJSON', {
  virtuals: true,
  transform: function(doc, ret) {
    delete ret.__v;
    return ret;
  }
});

module.exports = mongoose.model('VerificationRequest', verificationRequestSchema);
