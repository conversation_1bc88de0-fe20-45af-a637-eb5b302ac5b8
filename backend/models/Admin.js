const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const { isAccountLockingEnabled } = require('../config/security');

const adminSchema = new mongoose.Schema({
  fullName: {
    type: String,
    required: [true, 'Full name is required'],
    trim: true,
    minlength: [2, 'Full name must be at least 2 characters long'],
    maxlength: [100, 'Full name cannot exceed 100 characters']
  },
  email: {
    type: String,
    required: [true, 'Email is required'],
    lowercase: true,
    trim: true,
    match: [
      /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
      'Please provide a valid email address'
    ]
  },
  password: {
    type: String,
    required: [true, 'Password is required'],
    minlength: [8, 'Password must be at least 8 characters long'],
    validate: {
      validator: function(password) {
        // Only validate if it's a string (to avoid validating hashed passwords)
        if (typeof password !== 'string') return true;
        // Check for at least one uppercase, one lowercase, and one number
        return /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$/.test(password);
      },
      message: 'Password must contain at least one uppercase letter, one lowercase letter, and one number'
    }
  },
  role: {
    type: String,
    enum: ['admin', 'super_admin'],
    default: 'admin'
  },
  status: {
    type: String,
    enum: ['active', 'suspended', 'inactive'],
    default: 'active'
  },
  
  // Admin-specific permissions
  permissions: {
    users: {
      view: { type: Boolean, default: true },
      create: { type: Boolean, default: false },
      edit: { type: Boolean, default: true },
      delete: { type: Boolean, default: false },
      suspend: { type: Boolean, default: true }
    },
    barbers: {
      view: { type: Boolean, default: true },
      approve: { type: Boolean, default: true },
      reject: { type: Boolean, default: true },
      suspend: { type: Boolean, default: true },
      edit: { type: Boolean, default: true }
    },
    payments: {
      view: { type: Boolean, default: true },
      process: { type: Boolean, default: true },
      refund: { type: Boolean, default: false }
    },
    system: {
      settings: { type: Boolean, default: false },
      logs: { type: Boolean, default: true },
      reports: { type: Boolean, default: true }
    }
  },

  // OTP for password reset and security operations
  otp: {
    code: {
      type: String,
      default: null
    },
    expiresAt: {
      type: Date,
      default: null
    },
    attempts: {
      type: Number,
      default: 0
    },
    lastAttempt: {
      type: Date,
      default: null
    },
    purpose: {
      type: String,
      enum: ['password_reset', 'security_verification'],
      default: null
    }
  },

  // Account creation audit trail
  createdBy: {
    adminId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Admin',
      default: null
    },
    method: {
      type: String,
      enum: ['cli', 'admin_panel', 'system'],
      required: true
    },
    ipAddress: {
      type: String,
      default: null
    },
    userAgent: {
      type: String,
      default: null
    }
  },

  // Security and Login Tracking
  security: {
    failedLoginAttempts: {
      type: Number,
      default: 0
    },
    accountLockedUntil: {
      type: Date,
      default: null
    },
    lastFailedLogin: {
      type: Date,
      default: null
    },
    passwordChangedAt: {
      type: Date,
      default: null
    },
    suspiciousActivityCount: {
      type: Number,
      default: 0
    },
    lastSuspiciousActivity: {
      type: Date,
      default: null
    },
    twoFactorEnabled: {
      type: Boolean,
      default: false
    },
    lastPasswordChange: {
      type: Date,
      default: null
    }
  },

  // Login history tracking
  loginHistory: [{
    loginAt: {
      type: Date,
      default: Date.now
    },
    ipAddress: String,
    userAgent: String,
    deviceInfo: {
      type: String,
      default: null
    },
    location: {
      type: String,
      default: null
    },
    isNewDevice: {
      type: Boolean,
      default: false
    },
    sessionDuration: {
      type: Number, // in minutes
      default: null
    }
  }],

  // Admin activity tracking
  lastActivity: {
    type: Date,
    default: null
  },
  lastLogin: {
    type: Date,
    default: null
  }
}, {
  timestamps: true
});

// Indexes for performance and uniqueness
adminSchema.index({ email: 1 }, { unique: true });
adminSchema.index({ status: 1 });
adminSchema.index({ role: 1 });
adminSchema.index({ 'createdBy.adminId': 1 });
adminSchema.index({ lastActivity: 1 });

// Pre-save middleware to hash password
adminSchema.pre('save', async function(next) {
  // Only hash the password if it has been modified (or is new)
  if (!this.isModified('password')) return next();

  try {
    // Hash password with salt rounds of 12 (same as other user types)
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    
    // Update password change timestamp
    this.security.lastPasswordChange = new Date();
    
    next();
  } catch (error) {
    next(error);
  }
});

// Pre-save middleware to update lastActivity
adminSchema.pre('save', function(next) {
  this.lastActivity = new Date();
  next();
});

// Instance method to check password
adminSchema.methods.comparePassword = async function(candidatePassword) {
  if (!candidatePassword || !this.password) {
    return false;
  }
  
  try {
    const isMatch = await bcrypt.compare(candidatePassword, this.password);
    return isMatch;
  } catch (error) {
    console.error('Password comparison error:', error);
    return false;
  }
};

// Instance method to generate OTP
adminSchema.methods.generateOTP = function(purpose = 'password_reset') {
  // Generate 6-digit OTP
  const otp = Math.floor(100000 + Math.random() * 900000).toString();

  // Set OTP with 10-minute expiry for admin operations (longer than user OTP)
  this.otp.code = otp;
  this.otp.expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes
  this.otp.attempts = 0;
  this.otp.purpose = purpose;

  return otp;
};

// Instance method to verify OTP
adminSchema.methods.verifyOTP = function(candidateOTP, expectedPurpose = null) {
  // Check if OTP exists
  if (!this.otp.code) {
    return { success: false, message: 'No OTP found. Please request a new one.' };
  }

  // Check if OTP has expired
  if (new Date() > this.otp.expiresAt) {
    this.otp.code = null;
    this.otp.expiresAt = null;
    this.otp.purpose = null;
    return { success: false, message: 'OTP has expired. Please request a new one.' };
  }

  // Check purpose if specified
  if (expectedPurpose && this.otp.purpose !== expectedPurpose) {
    return { success: false, message: 'Invalid OTP purpose.' };
  }

  // Increment attempt count
  this.otp.attempts += 1;
  this.otp.lastAttempt = new Date();

  // Check if OTP matches
  if (this.otp.code !== candidateOTP) {
    return { success: false, message: 'Invalid OTP. Please try again.' };
  }

  // OTP is valid - clear it
  this.otp.code = null;
  this.otp.expiresAt = null;
  this.otp.attempts = 0;
  this.otp.purpose = null;

  return { success: true, message: 'OTP verified successfully.' };
};

// Instance method to check if account is locked
adminSchema.methods.isAccountLocked = function() {
  // Skip account locking if disabled via environment variable
  if (!isAccountLockingEnabled()) {
    return false;
  }

  return this.security.accountLockedUntil && this.security.accountLockedUntil > new Date();
};

// Instance method to increment failed login attempts
adminSchema.methods.incrementFailedLoginAttempts = function() {
  // Skip account locking if disabled via environment variable
  if (!isAccountLockingEnabled()) {
    // Still track failed attempts for logging/monitoring purposes
    this.security.failedLoginAttempts += 1;
    this.security.lastFailedLogin = new Date();
    return;
  }

  // If account is already locked and lock has expired, reset attempts
  if (this.security.accountLockedUntil && this.security.accountLockedUntil < new Date()) {
    this.security.failedLoginAttempts = 1;
    this.security.accountLockedUntil = null;
    this.security.lastFailedLogin = new Date();
    return;
  }

  // Increment failed attempts
  this.security.failedLoginAttempts += 1;
  this.security.lastFailedLogin = new Date();

  // Lock account after 5 failed attempts for 15 minutes
  if (this.security.failedLoginAttempts >= 5) {
    this.security.accountLockedUntil = new Date(Date.now() + 15 * 60 * 1000); // 15 minutes
  }
};

// Instance method to reset failed login attempts
adminSchema.methods.resetFailedLoginAttempts = function() {
  this.security.failedLoginAttempts = 0;
  this.security.accountLockedUntil = null;
  this.security.lastFailedLogin = null;
};

// Instance method to add login history entry
adminSchema.methods.addLoginHistory = function(ipAddress, userAgent, deviceInfo = null, location = null) {
  // Check if this is a new device
  const isNewDevice = !this.loginHistory.some(entry =>
    entry.userAgent === userAgent && entry.ipAddress === ipAddress
  );

  this.loginHistory.push({
    loginAt: new Date(),
    ipAddress,
    userAgent,
    deviceInfo,
    location,
    isNewDevice
  });

  // Keep only last 20 login records for admins (more than regular users)
  if (this.loginHistory.length > 20) {
    this.loginHistory = this.loginHistory.slice(-20);
  }

  this.lastLogin = new Date();
  return isNewDevice;
};

// Instance method to check permissions
adminSchema.methods.hasPermission = function(module, action) {
  if (this.role === 'super_admin') {
    return true; // Super admin has all permissions
  }

  if (!this.permissions[module]) {
    return false;
  }

  return this.permissions[module][action] === true;
};

// Instance method to update permissions
adminSchema.methods.updatePermissions = function(newPermissions) {
  // Merge new permissions with existing ones
  Object.keys(newPermissions).forEach(module => {
    if (this.permissions[module]) {
      Object.keys(newPermissions[module]).forEach(action => {
        this.permissions[module][action] = newPermissions[module][action];
      });
    }
  });
};

// Static method to find by email
adminSchema.statics.findByEmail = function(email) {
  return this.findOne({ email: email.toLowerCase() });
};

// Static method to create super admin
adminSchema.statics.createSuperAdmin = function(adminData) {
  const superAdminPermissions = {
    users: { view: true, create: true, edit: true, delete: true, suspend: true },
    barbers: { view: true, approve: true, reject: true, suspend: true, edit: true },
    payments: { view: true, process: true, refund: true },
    system: { settings: true, logs: true, reports: true }
  };

  return new this({
    ...adminData,
    role: 'super_admin',
    permissions: superAdminPermissions
  });
};

// Virtual for full profile
adminSchema.virtual('fullProfile').get(function() {
  return {
    id: this._id,
    fullName: this.fullName,
    email: this.email,
    role: this.role,
    status: this.status,
    permissions: this.permissions,
    lastLogin: this.lastLogin,
    lastActivity: this.lastActivity,
    createdAt: this.createdAt,
    createdBy: this.createdBy
  };
});

// Ensure virtual fields are serialized
adminSchema.set('toJSON', {
  virtuals: true,
  transform: function(doc, ret) {
    delete ret.password;
    delete ret.otp;
    delete ret.__v;
    delete ret.security; // Hide security details in JSON output
    return ret;
  }
});

module.exports = mongoose.model('Admin', adminSchema);
