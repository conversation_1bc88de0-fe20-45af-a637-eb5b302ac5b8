const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const { isAccountLockingEnabled } = require('../config/security');

const barberSchema = new mongoose.Schema({
  // Personal Information (Step 1)
  fullName: {
    type: String,
    required: [true, 'Full name is required'],
    trim: true,
    minlength: [2, 'Full name must be at least 2 characters long'],
    maxlength: [100, 'Full name cannot exceed 100 characters']
  },
  email: {
    type: String,
    required: [true, 'Email is required'],
    lowercase: true,
    trim: true,
    match: [
      /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
      'Please provide a valid email address'
    ]
  },
  phoneNumber: {
    type: String,
    required: [true, 'Phone number is required'],
    trim: true,
    validate: {
      validator: function(phone) {
        // Nigerian phone number validation
        return /^(\+234|234|0)?[789][01]\d{8}$/.test(phone.replace(/\s+/g, ''));
      },
      message: 'Please provide a valid Nigerian phone number'
    }
  },
  address: {
    type: String,
    required: [true, 'Address is required'],
    trim: true,
    minlength: [10, 'Address must be at least 10 characters long'],
    maxlength: [500, 'Address cannot exceed 500 characters']
  },

  // Business Information (Step 2)
  businessName: {
    type: String,
    trim: true,
    minlength: [3, 'Business name must be at least 3 characters long'],
    maxlength: [100, 'Business name cannot exceed 100 characters']
  },
  documents: {
    cacCertificate: {
      url: String,
      publicId: String,
      originalName: String,
      uploadedAt: Date
    },
    ninDocument: {
      url: String,
      publicId: String,
      originalName: String,
      uploadedAt: Date
    },
    passportPhoto: {
      url: String,
      publicId: String,
      originalName: String,
      uploadedAt: Date
    }
  },

  // Services offered by the barber
  services: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Service'
  }],

  // Security Information (Step 3)
  password: {
    type: String,
    minlength: [8, 'Password must be at least 8 characters long'],
    validate: {
      validator: function(password) {
        // Only validate if password is being set (not required initially)
        if (!password) return true;
        // Check for at least one uppercase, one lowercase, and one number
        return /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$/.test(password);
      },
      message: 'Password must contain at least one uppercase letter, one lowercase letter, and one number'
    }
  },

  // Registration Status Tracking
  registrationStatus: {
    type: String,
    enum: [
      'step1_completed',
      'step2_completed', 
      'step3_completed',
      'pending_verification',
      'verified',
      'rejected',
      'suspended',
      'inactive'
    ],
    default: 'step1_completed'
  },

  // Account Status (for banning/suspending verified accounts)
  status: {
    type: String,
    enum: ['active', 'banned', 'suspended', 'inactive'],
    default: 'active'
  },
  
  // OTP for email verification
  otp: {
    code: {
      type: String,
      default: null
    },
    expiresAt: {
      type: Date,
      default: null
    },
    attempts: {
      type: Number,
      default: 0
    },
    lastAttempt: {
      type: Date,
      default: null
    }
  },

  // Verification Details
  verification: {
    submittedAt: Date,
    reviewedAt: Date,
    reviewedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    rejectionReason: String,
    verificationRequestId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'VerificationRequest'
    }
  },

  // Profile Information (after verification)
  profile: {
    bio: String,
    specialties: [String],
    experience: String, // years of experience as string
    serviceArea: String, // areas where barber provides services
    profileImage: String, // deprecated - use profilePicture instead
    profilePicture: String, // URL of the profile picture
    profilePicturePublicId: String, // Cloudinary public ID for deletion
    portfolio: [{
      url: String,
      publicId: String,
      type: {
        type: String,
        enum: ['image', 'video'],
        default: 'image'
      },
      uploadedAt: {
        type: Date,
        default: Date.now
      }
    }],
    businessHours: {
      monday: { open: String, close: String, isOpen: Boolean },
      tuesday: { open: String, close: String, isOpen: Boolean },
      wednesday: { open: String, close: String, isOpen: Boolean },
      thursday: { open: String, close: String, isOpen: Boolean },
      friday: { open: String, close: String, isOpen: Boolean },
      saturday: { open: String, close: String, isOpen: Boolean },
      sunday: { open: String, close: String, isOpen: Boolean }
    },
    location: {
      type: {
        type: String,
        enum: ['Point'],
        default: 'Point'
      },
      coordinates: {
        type: [Number], // [longitude, latitude]
        default: [0, 0]
      },
      address: String
    }
  },

  // Security and Login Tracking
  security: {
    failedLoginAttempts: {
      type: Number,
      default: 0
    },
    accountLockedUntil: {
      type: Date,
      default: null
    },
    lastFailedLogin: {
      type: Date,
      default: null
    },
    passwordChangedAt: {
      type: Date,
      default: null
    },
    suspiciousActivityCount: {
      type: Number,
      default: 0
    },
    lastSuspiciousActivity: {
      type: Date,
      default: null
    }
  },
  loginHistory: [{
    loginAt: {
      type: Date,
      default: Date.now
    },
    ipAddress: String,
    userAgent: String,
    deviceInfo: {
      type: String,
      default: null
    },
    location: {
      type: String,
      default: null
    },
    isNewDevice: {
      type: Boolean,
      default: false
    }
  }],

  // Profile Visibility Control
  isProfileActive: {
    type: Boolean,
    default: false,
    index: true
  },

  // Rating and Review System
  rating: {
    type: Number,
    default: 0,
    min: 0,
    max: 5,
    index: true
  },
  totalReviews: {
    type: Number,
    default: 0,
    min: 0
  },

  // Timestamps
  emailVerifiedAt: {
    type: Date,
    default: null
  },
  verifiedAt: {
    type: Date,
    default: null
  },
  lastLogin: {
    type: Date,
    default: null
  },

  notificationPreferences: {
    bookingAlerts: {
      email: { type: Boolean, default: true },
      push: { type: Boolean, default: true },
      sms: { type: Boolean, default: false }
    },
    customerMessages: {
      email: { type: Boolean, default: true },
      push: { type: Boolean, default: true },
      sms: { type: Boolean, default: true }
    },
    paymentNotifications: {
      email: { type: Boolean, default: true },
      push: { type: Boolean, default: false },
      sms: { type: Boolean, default: true }
    }
  }
}, {
  timestamps: true
});

// Indexes
barberSchema.index({ email: 1 }, { unique: true });
barberSchema.index({ registrationStatus: 1 });
barberSchema.index({ status: 1 });
barberSchema.index({ phoneNumber: 1 });
barberSchema.index({ 'verification.submittedAt': 1 });
barberSchema.index({ 'profile.location': '2dsphere' });

// Pre-save middleware to hash password
barberSchema.pre('save', async function(next) {
  // Only hash the password if it has been modified (or is new) and exists
  if (!this.isModified('password') || !this.password) return next();
  
  try {
    // Hash password with salt rounds of 12
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Instance method to check password
barberSchema.methods.comparePassword = async function(candidatePassword) {
  try {
    if (!this.password) {
      throw new Error('Password not set');
    }
    return await bcrypt.compare(candidatePassword, this.password);
  } catch (error) {
    throw new Error('Password comparison failed');
  }
};

// Instance method to generate OTP
barberSchema.methods.generateOTP = function() {
  // Generate 6-digit OTP
  const otp = Math.floor(100000 + Math.random() * 900000).toString();
  
  // Set OTP with 5-minute expiry
  this.otp.code = otp;
  this.otp.expiresAt = new Date(Date.now() + 5 * 60 * 1000); // 5 minutes
  this.otp.attempts = 0;
  
  return otp;
};

// Instance method to verify OTP
barberSchema.methods.verifyOTP = function(candidateOTP) {
  // Check if OTP exists
  if (!this.otp.code) {
    return { success: false, message: 'No OTP found. Please request a new one.' };
  }

  // Check if OTP has expired
  if (new Date() > this.otp.expiresAt) {
    this.otp.code = null;
    this.otp.expiresAt = null;
    return { success: false, message: 'OTP has expired. Please request a new one.' };
  }

  // Increment attempt count
  this.otp.attempts += 1;
  this.otp.lastAttempt = new Date();

  // Check if OTP matches
  if (this.otp.code !== candidateOTP) {
    return { success: false, message: 'Invalid OTP. Please try again.' };
  }

  // OTP is valid - clear it and mark email as verified
  this.otp.code = null;
  this.otp.expiresAt = null;
  this.otp.attempts = 0;
  this.emailVerifiedAt = new Date();

  return { success: true, message: 'OTP verified successfully.' };
};

// Instance method to check if account is locked
barberSchema.methods.isAccountLocked = function() {
  // Skip account locking if disabled via environment variable
  if (!isAccountLockingEnabled()) {
    return false;
  }

  return this.security.accountLockedUntil && this.security.accountLockedUntil > new Date();
};

// Instance method to increment failed login attempts
barberSchema.methods.incrementFailedLoginAttempts = function() {
  // Skip account locking if disabled via environment variable
  if (!isAccountLockingEnabled()) {
    // Still track failed attempts for logging/monitoring purposes
    this.security.failedLoginAttempts += 1;
    this.security.lastFailedLogin = new Date();
    return;
  }

  // If account is already locked and lock has expired, reset attempts
  if (this.security.accountLockedUntil && this.security.accountLockedUntil < new Date()) {
    this.security.failedLoginAttempts = 1;
    this.security.accountLockedUntil = null;
    this.security.lastFailedLogin = new Date();
    return;
  }

  this.security.failedLoginAttempts += 1;
  this.security.lastFailedLogin = new Date();

  // Lock account after 5 failed attempts for 15 minutes
  if (this.security.failedLoginAttempts >= 5) {
    this.security.accountLockedUntil = new Date(Date.now() + 15 * 60 * 1000); // 15 minutes
  }
};

// Instance method to reset failed login attempts
barberSchema.methods.resetFailedLoginAttempts = function() {
  this.security.failedLoginAttempts = 0;
  this.security.accountLockedUntil = null;
  this.security.lastFailedLogin = null;
};

// Instance method to add login history entry
barberSchema.methods.addLoginHistory = function(ipAddress, userAgent, deviceInfo = null, location = null) {
  // Check if this is a new device
  const isNewDevice = !this.loginHistory.some(entry =>
    entry.userAgent === userAgent && entry.ipAddress === ipAddress
  );

  this.loginHistory.push({
    loginAt: new Date(),
    ipAddress,
    userAgent,
    deviceInfo,
    location,
    isNewDevice
  });

  // Keep only last 10 login records
  if (this.loginHistory.length > 10) {
    this.loginHistory = this.loginHistory.slice(-10);
  }

  this.lastLogin = new Date();
  return isNewDevice;
};

// Static method to find barber by email
barberSchema.statics.findByEmail = async function(email) {
  return this.findOne({ email }).select('+password');
};

// Virtual for full profile
barberSchema.virtual('fullProfile').get(function() {
  return {
    id: this._id,
    fullName: this.fullName,
    email: this.email,
    phoneNumber: this.phoneNumber,
    address: this.address,
    businessName: this.businessName,
    registrationStatus: this.registrationStatus,
    profile: this.profile,
    emailVerifiedAt: this.emailVerifiedAt,
    verifiedAt: this.verifiedAt,
    createdAt: this.createdAt
  };
});

// Ensure virtual fields are serialized
barberSchema.set('toJSON', {
  virtuals: true,
  transform: function(doc, ret) {
    delete ret.password;
    delete ret.otp;
    delete ret.__v;
    return ret;
  }
});

module.exports = mongoose.model('Barber', barberSchema);
