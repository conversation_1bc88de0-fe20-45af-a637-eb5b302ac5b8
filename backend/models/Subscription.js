const mongoose = require('mongoose');
const paymentConfig = require('../config/payment');

const subscriptionSchema = new mongoose.Schema({
  barber: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Barber',
    required: true,
    unique: true
  },
  status: {
    type: String,
    enum: ['active', 'expired', 'suspended', 'cancelled', 'never_subscribed', 'pending'],
    default: 'never_subscribed'
  },
  currentPeriod: {
    startDate: {
      type: Date,
      required: false,
      default: null
    },
    endDate: {
      type: Date,
      required: false,
      default: null
    }
  },
  subscriptionFee: {
    type: Number,
    required: true,
    default: () => paymentConfig.subscriptionFee // Use fee from config
  },
  // Card tokenization for recurring payments
  cardToken: {
    type: String,
    required: false,
    select: false // Don't include in regular queries for security
  },
  nextBillingDate: {
    type: Date,
    required: false,
    index: true // For efficient cron job queries
  },
  recurringBilling: {
    enabled: {
      type: Boolean,
      default: false
    },
    failedAttempts: {
      type: Number,
      default: 0
    },
    lastFailedAt: {
      type: Date
    },
    maxRetries: {
      type: Number,
      default: 3
    }
  },
  paymentHistory: [{
    transactionReference: {
      type: String,
      required: true
    },
    paymentReference: {
      type: String,
      required: true
    },
    amount: {
      type: Number,
      required: true
    },
    paymentDate: {
      type: Date,
      required: true
    },
    periodStart: {
      type: Date,
      required: true
    },
    periodEnd: {
      type: Date,
      required: true
    },
    paymentMethod: {
      type: String,
      enum: ['monnify', 'other'],
      default: 'monnify'
    },
    paymentStatus: {
      type: String,
      enum: ['pending', 'completed', 'failed', 'refunded'],
      default: 'pending'
    },
    isRecurring: {
      type: Boolean,
      default: false
    },
    cardTokenUsed: {
      type: String,
      required: false
    }
  }],
  renewalReminders: [{
    type: {
      type: String,
      enum: ['3_days_before', 'expiry_day', 'expired'],
      required: true
    },
    sentAt: {
      type: Date,
      required: true
    },
    emailSent: {
      type: Boolean,
      default: false
    }
  }],
  lastRenewalDate: {
    type: Date
  },
  autoRenewal: {
    enabled: {
      type: Boolean,
      default: false
    },
    paymentMethod: String
  },
  suspensionReason: {
    type: String,
    trim: true
  },
  notes: {
    type: String,
    trim: true
  }
}, {
  timestamps: true,
  toJSON: {
    virtuals: true,
    transform: function(doc, ret) {
      // Always use current subscription fee from config
      ret.subscriptionFee = paymentConfig.subscriptionFee;
      delete ret.__v;
      return ret;
    }
  }
});

// Indexes for better query performance
subscriptionSchema.index({ 'currentPeriod.endDate': 1 });
subscriptionSchema.index({ status: 1, 'currentPeriod.endDate': 1 });

// Virtual to check if subscription is active
subscriptionSchema.virtual('isActive').get(function() {
  return this.status === 'active' && 
         this.currentPeriod.endDate && 
         new Date() <= this.currentPeriod.endDate;
});

// Virtual to check days remaining
subscriptionSchema.virtual('daysRemaining').get(function() {
  if (this.status !== 'active' || !this.currentPeriod.endDate) return 0;
  const now = new Date();
  const endDate = this.currentPeriod.endDate;
  const diffTime = endDate - now;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return Math.max(0, diffDays);
});

// Instance method to activate subscription
subscriptionSchema.methods.activateSubscription = function(paymentData) {
  const { transactionReference, paymentReference, amount, paymentDate, cardToken, isRecurring = false } = paymentData;
  
  const startDate = new Date(paymentDate);
  const endDate = new Date(startDate);
  endDate.setDate(endDate.getDate() + 30); // 30 days from payment date
  
  // Transition from never_subscribed, expired, or pending to active
  this.status = 'active';
  this.currentPeriod = {
    startDate,
    endDate
  };
  this.lastRenewalDate = paymentDate;
  
  // Set up recurring billing if card token is provided
  if (cardToken) {
    this.cardToken = cardToken;
    this.nextBillingDate = new Date(endDate); // Next billing on expiry
    this.recurringBilling.enabled = true;
    this.recurringBilling.failedAttempts = 0;
    this.recurringBilling.lastFailedAt = null;
  }
  
  // Add to payment history
  this.paymentHistory.push({
    transactionReference,
    paymentReference,
    amount,
    paymentDate,
    periodStart: startDate,
    periodEnd: endDate,
    paymentMethod: 'monnify',
    paymentStatus: 'completed',
    isRecurring,
    cardTokenUsed: cardToken ? 'yes' : undefined
  });
  
  // Clear previous reminders
  this.renewalReminders = [];
  
  return this;
};

// Instance method to expire subscription
subscriptionSchema.methods.expireSubscription = function() {
  this.status = 'expired';
  return this;
};

// Instance method to handle recurring payment success
subscriptionSchema.methods.processRecurringPaymentSuccess = function(paymentData) {
  const { transactionReference, paymentReference, amount, paymentDate } = paymentData;
  
  const startDate = new Date(paymentDate);
  const endDate = new Date(startDate);
  endDate.setDate(endDate.getDate() + 30); // 30 days from payment date
  
  // Update subscription period
  this.currentPeriod = {
    startDate,
    endDate
  };
  this.lastRenewalDate = paymentDate;
  this.nextBillingDate = new Date(endDate); // Set next billing date
  
  // Reset failed attempts
  this.recurringBilling.failedAttempts = 0;
  this.recurringBilling.lastFailedAt = null;
  
  // Add to payment history
  this.paymentHistory.push({
    transactionReference,
    paymentReference,
    amount,
    paymentDate,
    periodStart: startDate,
    periodEnd: endDate,
    paymentMethod: 'monnify',
    paymentStatus: 'completed',
    isRecurring: true,
    cardTokenUsed: 'yes'
  });
  
  // Clear previous reminders
  this.renewalReminders = [];
  
  return this;
};

// Instance method to handle recurring payment failure
subscriptionSchema.methods.processRecurringPaymentFailure = function(failureData) {
  const { transactionReference, paymentReference, amount, failureDate, reason } = failureData;
  
  // Increment failed attempts
  this.recurringBilling.failedAttempts += 1;
  this.recurringBilling.lastFailedAt = failureDate;
  
  // Add failed payment to history
  this.paymentHistory.push({
    transactionReference,
    paymentReference,
    amount,
    paymentDate: failureDate,
    periodStart: new Date(),
    periodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
    paymentMethod: 'monnify',
    paymentStatus: 'failed',
    isRecurring: true,
    cardTokenUsed: 'yes',
    failureReason: reason
  });
  
  // If max retries reached, disable recurring billing and expire subscription
  if (this.recurringBilling.failedAttempts >= this.recurringBilling.maxRetries) {
    this.recurringBilling.enabled = false;
    this.status = 'expired';
    this.cardToken = null; // Remove card token for security
  }
  
  return this;
};

// Instance method to check if reminder should be sent
subscriptionSchema.methods.shouldSendReminder = function(reminderType) {
  const existingReminder = this.renewalReminders.find(r => r.type === reminderType);
  
  if (existingReminder) {
    return false; // Already sent
  }
  
  // If no end date (never subscribed), no reminders needed
  if (!this.currentPeriod.endDate) {
    return false;
  }
  
  const now = new Date();
  const endDate = this.currentPeriod.endDate;
  const diffTime = endDate - now;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  switch (reminderType) {
    case '3_days_before':
      return diffDays <= 3 && diffDays > 0;
    case 'expiry_day':
      return diffDays <= 0 && diffDays > -1;
    case 'expired':
      return diffDays < 0;
    default:
      return false;
  }
};

// Instance method to mark reminder as sent
subscriptionSchema.methods.markReminderSent = function(reminderType) {
  this.renewalReminders.push({
    type: reminderType,
    sentAt: new Date(),
    emailSent: true
  });
  return this;
};

// Static method to get expired subscriptions
subscriptionSchema.statics.getExpiredSubscriptions = function() {
  const now = new Date();
  return this.find({
    status: 'active',
    'currentPeriod.endDate': { $lt: now }
  }).populate('barber');
};

// Static method to get subscriptions needing reminders
subscriptionSchema.statics.getSubscriptionsNeedingReminders = function() {
  const now = new Date();
  const threeDaysFromNow = new Date(now.getTime() + (3 * 24 * 60 * 60 * 1000));
  
  return this.find({
    status: 'active',
    'currentPeriod.endDate': { $lte: threeDaysFromNow }
  }).populate('barber');
};

// Static method to get subscriptions due for recurring billing
subscriptionSchema.statics.getSubscriptionsDueForBilling = function() {
  const now = new Date();
  
  return this.find({
    status: 'active',
    'recurringBilling.enabled': true,
    nextBillingDate: { $lte: now },
    cardToken: { $exists: true, $ne: null }
  }).populate('barber').select('+cardToken'); // Include cardToken in results
};

// Static method to create or get subscription
subscriptionSchema.statics.getOrCreate = async function(barberId) {
  let subscription = await this.findOne({ barber: barberId });
  
  if (!subscription) {
    // New barber - never subscribed before
    const now = new Date();
    subscription = new this({
      barber: barberId,
      status: 'never_subscribed',
      currentPeriod: {
        startDate: null,
        endDate: null
      },
      paymentHistory: []
    });
    await subscription.save();
  } else {
    // Existing subscription - determine correct status based on payment history
    const hasEverPaid = subscription.paymentHistory && subscription.paymentHistory.length > 0;
    
    if (subscription.status === 'active' && new Date() > subscription.currentPeriod.endDate) {
      // Active subscription that has expired
      subscription.status = 'expired';
      await subscription.save();
    } else if (!hasEverPaid && subscription.status === 'expired') {
      // User has never made a payment but has 'expired' status - fix this
      subscription.status = 'never_subscribed';
      subscription.currentPeriod.startDate = null;
      subscription.currentPeriod.endDate = null;
      await subscription.save();
    }
  }
  
  return subscription;
};

module.exports = mongoose.model('Subscription', subscriptionSchema); 