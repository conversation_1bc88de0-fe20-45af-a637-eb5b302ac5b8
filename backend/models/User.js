const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const { isAccountLockingEnabled } = require('../config/security');

const userSchema = new mongoose.Schema({
  fullName: {
    type: String,
    required: [true, 'Full name is required'],
    trim: true,
    minlength: [2, 'Full name must be at least 2 characters long']
  },
  email: {
    type: String,
    required: [true, 'Email is required'],
    unique: true,
    lowercase: true,
    trim: true,
    match: [
      /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
      'Please provide a valid email address'
    ]
  },
  password: {
    type: String,
    required: [true, 'Password is required'],
    minlength: [8, 'Password must be at least 8 characters long'],
    validate: {
      validator: function(password) {
        // Check for at least one uppercase, one lowercase, and one number
        return /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$/.test(password);
      },
      message: 'Password must contain at least one uppercase letter, one lowercase letter, and one number'
    },
    select: false
  },
  role: {
    type: String,
    enum: ['user', 'barber', 'admin'],
    default: 'user'
  },
  status: {
    type: String,
    enum: ['pending_verification', 'verified', 'suspended', 'inactive'],
    default: 'pending_verification'
  },
  suspensionReason: {
    type: String,
    default: null
  },
  otp: {
    code: {
      type: String,
      default: null
    },
    expiresAt: {
      type: Date,
      default: null
    },
    attempts: {
      type: Number,
      default: 0
    },
    lastAttempt: {
      type: Date,
      default: null
    }
  },
  profile: {
    phoneNumber: {
      type: String,
      default: null
    },
    address: {
      type: String,
      default: null
    },
    profileImage: {
      type: String,
      default: null
    },
    dateOfBirth: {
      type: Date,
      default: null
    }
  },
  // Favorite barbers
  favorites: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Barber'
  }],
  // Security and Login Tracking
  security: {
    failedLoginAttempts: {
      type: Number,
      default: 0
    },
    accountLockedUntil: {
      type: Date,
      default: null
    },
    lastFailedLogin: {
      type: Date,
      default: null
    },
    passwordChangedAt: {
      type: Date,
      default: null
    },
    suspiciousActivityCount: {
      type: Number,
      default: 0
    },
    lastSuspiciousActivity: {
      type: Date,
      default: null
    }
  },
  loginHistory: [{
    loginAt: {
      type: Date,
      default: Date.now
    },
    ipAddress: String,
    userAgent: String,
    deviceInfo: {
      type: String,
      default: null
    },
    location: {
      type: String,
      default: null
    },
    isNewDevice: {
      type: Boolean,
      default: false
    }
  }],
  lastLogin: {
    type: Date,
    default: null
  },
  emailVerifiedAt: {
    type: Date,
    default: null
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Remove duplicate email index since it's already defined in the schema
userSchema.index({ status: 1 });
userSchema.index({ role: 1 });

// Pre-save middleware to hash password
userSchema.pre('save', async function(next) {
  // Only hash the password if it has been modified (or is new)
  if (!this.isModified('password')) return next();
  
  try {
    // Hash password with salt rounds of 12
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Pre-save middleware to update updatedAt
userSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Instance method to check password
userSchema.methods.comparePassword = async function(candidatePassword) {
  try {
    return await bcrypt.compare(candidatePassword, this.password);
  } catch (error) {
    throw new Error('Password comparison failed');
  }
};

// Instance method to generate OTP
userSchema.methods.generateOTP = function() {
  // Generate 6-digit OTP
  const otp = Math.floor(100000 + Math.random() * 900000).toString();
  
  // Set OTP with 5-minute expiry (as per requirements)
  this.otp.code = otp;
  this.otp.expiresAt = new Date(Date.now() + 5 * 60 * 1000); // 5 minutes
  this.otp.attempts = 0;
  
  return otp;
};

// Instance method to verify OTP
userSchema.methods.verifyOTP = function(providedOTP) {
  // Check if OTP exists
  if (!this.otp.code) {
    return { success: false, message: 'No OTP found. Please request a new one.' };
  }
  
  // Check if OTP has expired
  if (new Date() > this.otp.expiresAt) {
    return { success: false, message: 'OTP has expired. Please request a new one.' };
  }
  
  // Check if too many attempts
  if (this.otp.attempts >= 5) {
    return { success: false, message: 'Too many OTP attempts. Please request a new one.' };
  }
  
  // Increment attempts
  this.otp.attempts += 1;
  this.otp.lastAttempt = new Date();
  
  // Verify OTP
  if (this.otp.code === providedOTP) {
    // Clear OTP data after successful verification
    this.otp.code = null;
    this.otp.expiresAt = null;
    this.otp.attempts = 0;
    this.otp.lastAttempt = null;
    
    return { success: true, message: 'OTP verified successfully.' };
  }
  
  return { success: false, message: 'Invalid OTP. Please try again.' };
};

// Instance method to clear OTP
userSchema.methods.clearOTP = function() {
  this.otp.code = null;
  this.otp.expiresAt = null;
  this.otp.attempts = 0;
  this.otp.lastAttempt = null;
};

// Instance method to check if account is locked
userSchema.methods.isAccountLocked = function() {
  // Skip account locking if disabled via environment variable
  if (!isAccountLockingEnabled()) {
    return false;
  }

  return this.security.accountLockedUntil && this.security.accountLockedUntil > new Date();
};

// Instance method to increment failed login attempts
userSchema.methods.incrementFailedLoginAttempts = function() {
  // Skip account locking if disabled via environment variable
  if (!isAccountLockingEnabled()) {
    // Still track failed attempts for logging/monitoring purposes
    this.security.failedLoginAttempts += 1;
    this.security.lastFailedLogin = new Date();
    return;
  }

  // If account is already locked and lock has expired, reset attempts
  if (this.security.accountLockedUntil && this.security.accountLockedUntil < new Date()) {
    this.security.failedLoginAttempts = 1;
    this.security.accountLockedUntil = null;
    this.security.lastFailedLogin = new Date();
    return;
  }

  this.security.failedLoginAttempts += 1;
  this.security.lastFailedLogin = new Date();

  // Lock account after 5 failed attempts for 15 minutes
  if (this.security.failedLoginAttempts >= 5) {
    this.security.accountLockedUntil = new Date(Date.now() + 15 * 60 * 1000); // 15 minutes
  }
};

// Instance method to reset failed login attempts
userSchema.methods.resetFailedLoginAttempts = function() {
  this.security.failedLoginAttempts = 0;
  this.security.accountLockedUntil = null;
  this.security.lastFailedLogin = null;
};

// Instance method to add login history entry
userSchema.methods.addLoginHistory = function(ipAddress, userAgent, deviceInfo = null, location = null) {
  // Check if this is a new device
  const isNewDevice = !this.loginHistory.some(entry =>
    entry.userAgent === userAgent && entry.ipAddress === ipAddress
  );

  this.loginHistory.push({
    loginAt: new Date(),
    ipAddress,
    userAgent,
    deviceInfo,
    location,
    isNewDevice
  });

  // Keep only last 10 login records
  if (this.loginHistory.length > 10) {
    this.loginHistory = this.loginHistory.slice(-10);
  }

  this.lastLogin = new Date();
  return isNewDevice;
};

// Static method to find user by email
userSchema.statics.findByEmail = async function(email) {
  return this.findOne({ email }).select('+password');
};

// Static method to check if email exists across all user types
userSchema.statics.checkEmailExists = async function(email) {
  const User = this;
  const mongoose = require('mongoose');

  try {
    // Check if Barber model exists to avoid circular dependency issues
    let existingBarber = null;
    if (mongoose.models.Barber) {
      const Barber = mongoose.model('Barber');
      existingBarber = await Barber.findByEmail(email);
    }

    const existingUser = await User.findByEmail(email);

    return {
      exists: !!(existingUser || existingBarber),
      userType: existingUser ? 'user' : existingBarber ? 'barber' : null,
      user: existingUser || existingBarber || null
    };
  } catch (error) {
    console.error('Error checking email existence:', error);
    // Fallback to just checking users if there's an issue
    const existingUser = await User.findByEmail(email);
    return {
      exists: !!existingUser,
      userType: existingUser ? 'user' : null,
      user: existingUser || null
    };
  }
};

// Virtual for full profile
userSchema.virtual('fullProfile').get(function() {
  return {
    id: this._id,
    fullName: this.fullName,
    email: this.email,
    role: this.role,
    status: this.status,
    profile: this.profile,
    lastLogin: this.lastLogin,
    emailVerifiedAt: this.emailVerifiedAt,
    createdAt: this.createdAt
  };
});

// Ensure virtual fields are serialized
userSchema.set('toJSON', {
  virtuals: true,
  transform: function(doc, ret) {
    delete ret.password;
    delete ret.otp;
    delete ret.__v;
    return ret;
  }
});

module.exports = mongoose.model('User', userSchema);
