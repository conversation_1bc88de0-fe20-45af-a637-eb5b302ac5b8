const mongoose = require('mongoose');

const refundSchema = new mongoose.Schema({
  payment: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Payment',
    required: true
  },
  booking: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Booking',
    required: true
  },
  amount: {
    type: Number,
    required: true
  },
  reason: {
    type: String,
    required: true,
    enum: [
      'BARBER_CANCELLATION',
      'USER_CANCELLATION_EARLY',
      'USER_CANCELLATION_LATE',
      'SERVICE_NOT_PROVIDED',
      'QUALITY_DISPUTE',
      'TECHNICAL_ISSUE',
      'DISPUTE_RESOLUTION',
      'OTHER'
    ]
  },
  status: {
    type: String,
    enum: ['PENDING', 'PROCESSING', 'COMPLETED', 'FAILED'],
    default: 'PENDING'
  },
  refundReference: {
    type: String,
    required: true,
    unique: true
  },
  disputeId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Dispute',
    required: false
  },
  processedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin',
    required: false
  },
  processedAt: {
    type: Date,
    required: false
  },
  notes: {
    type: String,
    required: false
  },
  metadata: {
    originalPaymentMethod: String,
    refundTransactionId: String,
    gatewayResponse: Object
  }
}, {
  timestamps: true
});

// Generate unique refund reference
refundSchema.pre('save', function(next) {
  if (this.isNew) {
    this.refundReference = `REF-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
  next();
});

// Indexes for better query performance
refundSchema.index({ status: 1 });
refundSchema.index({ payment: 1 });
refundSchema.index({ booking: 1 });
refundSchema.index({ disputeId: 1 });

const Refund = mongoose.model('Refund', refundSchema);

module.exports = Refund; 