const nodemailer = require('nodemailer');

// Create transporter based on provider (gmail or cpanel)
const createTransporter = () => {
  const emailProvider = process.env.EMAIL_PROVIDER?.toLowerCase() || 'cpanel';

  let config;

  if (emailProvider === 'gmail') {
    config = {
      service: 'gmail',
      auth: {
        user: process.env.GMAIL_USER,
        pass: process.env.GMAIL_APP_PASSWORD // This should be an App Password, not your regular Gmail password
      }
    };
    console.log('Using Gmail SMTP configuration');
  } else {
    // Default cPanel configuration
    config = {
      host: process.env.EMAIL_HOST,
      port: parseInt(process.env.EMAIL_PORT) || 465,
      secure: parseInt(process.env.EMAIL_PORT) === 465,
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS
      }
    };

    // For port 587, add additional security options
    if (parseInt(process.env.EMAIL_PORT) === 587) {
      config.tls = {
        ciphers: 'SSLv3',
        rejectUnauthorized: false
      };
    }
    console.log('Using cPanel SMTP configuration');
  }

  return nodemailer.createTransport(config);
};

// Verify email configuration
const verifyEmailConfig = async () => {
  try {
    const provider = process.env.EMAIL_PROVIDER?.toLowerCase() || 'cpanel';
    const transporter = createTransporter();
    await transporter.verify();
    console.log(`Email configuration verified successfully (using ${provider})`);

    // Log current settings (hiding sensitive data)
    const settings = provider === 'gmail' ? {
      provider: 'gmail',
      user: process.env.GMAIL_USER ? '(set)' : '(not set)',
      appPassword: process.env.GMAIL_APP_PASSWORD ? '(set)' : '(not set)'
    } : {
      provider: 'cpanel',
      host: process.env.EMAIL_HOST,
      port: process.env.EMAIL_PORT,
      secure: parseInt(process.env.EMAIL_PORT) === 465,
      user: process.env.EMAIL_USER ? '(set)' : '(not set)',
      pass: process.env.EMAIL_PASS ? '(set)' : '(not set)'
    };

    console.log('Current email settings:', settings);
    return true;
  } catch (error) {
    console.error('Email configuration verification failed:', error.message);
    return false;
  }
};

module.exports = {
  createTransporter,
  verifyEmailConfig
};
