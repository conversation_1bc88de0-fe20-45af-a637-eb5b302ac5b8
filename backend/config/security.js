/**
 * Security Configuration Module
 * 
 * This module provides environment-based configuration toggles for security features.
 * It allows developers to disable security restrictions during development/testing
 * while ensuring production safety with secure defaults.
 */

/**
 * Parse environment variable as boolean with safe defaults
 * @param {string} envVar - Environment variable name
 * @param {boolean} defaultValue - Default value if env var is not set or invalid
 * @returns {boolean} - Parsed boolean value
 */
const parseEnvBoolean = (envVar, defaultValue = true) => {
  const value = process.env[envVar];
  
  // If environment variable is not set, return default (secure) value
  if (value === undefined || value === null || value === '') {
    return defaultValue;
  }
  
  // Parse string to boolean - only 'false' (case insensitive) returns false
  // This ensures production safety - any invalid value defaults to true
  return value.toLowerCase() !== 'false';
};

// Security constants
const MAX_LOGIN_ATTEMPTS = 5;
const LOCK_TIME = 15; // minutes
const MAX_OTP_ATTEMPTS = 5;

/**
 * Security configuration object with environment-based toggles
 */
const securityConfig = {
  // Account locking after failed login attempts
  accountLocking: {
    enabled: parseEnvBoolean('ENABLE_ACCOUNT_LOCKING', true),
    maxAttempts: MAX_LOGIN_ATTEMPTS,
    lockDurationMinutes: LOCK_TIME
  },
  
  // IP-based rate limiting for login attempts
  ipRateLimit: {
    enabled: parseEnvBoolean('ENABLE_IP_RATE_LIMITING', true),
    maxAttempts: MAX_LOGIN_ATTEMPTS,
    windowMinutes: 15
  },
  
  // OTP rate limiting (both IP-based and user-based)
  otpRateLimit: {
    enabled: parseEnvBoolean('ENABLE_OTP_RATE_LIMITING', true),
    maxAttempts: MAX_OTP_ATTEMPTS,
    windowMinutes: 15
  }
};

/**
 * Helper functions to check if security features are enabled
 */
const isAccountLockingEnabled = () => securityConfig.accountLocking.enabled;
const isIpRateLimitEnabled = () => securityConfig.ipRateLimit.enabled;
const isOtpRateLimitEnabled = () => securityConfig.otpRateLimit.enabled;

/**
 * Get security configuration for logging/debugging
 */
const getSecurityStatus = () => {
  return {
    accountLocking: securityConfig.accountLocking.enabled ? 'ENABLED' : 'DISABLED',
    ipRateLimit: securityConfig.ipRateLimit.enabled ? 'ENABLED' : 'DISABLED',
    otpRateLimit: securityConfig.otpRateLimit.enabled ? 'ENABLED' : 'DISABLED',
    environment: process.env.NODE_ENV || 'development'
  };
};

/**
 * Log security configuration on startup
 */
const logSecurityConfig = () => {
  const status = getSecurityStatus();
  console.log('🔒 Security Configuration:');
  console.log(`   Account Locking: ${status.accountLocking}`);
  console.log(`   IP Rate Limiting: ${status.ipRateLimit}`);
  console.log(`   OTP Rate Limiting: ${status.otpRateLimit}`);
  console.log(`   Environment: ${status.environment}`);
  
  // Warning for disabled security features in production
  if (process.env.NODE_ENV === 'production') {
    const disabledFeatures = [];
    if (!securityConfig.accountLocking.enabled) disabledFeatures.push('Account Locking');
    if (!securityConfig.ipRateLimit.enabled) disabledFeatures.push('IP Rate Limiting');
    if (!securityConfig.otpRateLimit.enabled) disabledFeatures.push('OTP Rate Limiting');
    
    if (disabledFeatures.length > 0) {
      console.warn('⚠️  WARNING: Security features disabled in production:', disabledFeatures.join(', '));
    }
  }
};

module.exports = {
  securityConfig,
  MAX_LOGIN_ATTEMPTS,
  LOCK_TIME,
  MAX_OTP_ATTEMPTS,
  isAccountLockingEnabled,
  isIpRateLimitEnabled,
  isOtpRateLimitEnabled,
  getSecurityStatus,
  logSecurityConfig
};
