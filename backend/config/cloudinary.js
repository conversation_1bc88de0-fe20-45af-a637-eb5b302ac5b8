const cloudinary = require('cloudinary').v2;

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
  secure: true
});

// Verify Cloudinary configuration
const verifyCloudinaryConfig = async () => {
  try {
    const result = await cloudinary.api.ping();
    console.log('Cloudinary configuration verified successfully');
    return true;
  } catch (error) {
    console.error('Cloudinary configuration verification failed:', error.message);
    return false;
  }
};

// Upload file to Cloudinary with organized folder structure
const uploadToCloudinary = async (fileBuffer, options = {}) => {
  try {
    const {
      folder = 'etch',
      resourceType = 'auto',
      publicId,
      transformation,
      tags = []
    } = options;

    const uploadOptions = {
      folder,
      resource_type: resourceType,
      use_filename: true,
      unique_filename: true,
      overwrite: false,
      tags: ['etch-platform', ...tags],
      access_mode: resourceType === 'raw' ? 'authenticated' : 'public'
    };

    if (publicId) {
      uploadOptions.public_id = publicId;
    }

    if (transformation && resourceType !== 'raw') {
      uploadOptions.transformation = transformation;
    }

    return new Promise((resolve, reject) => {
      cloudinary.uploader.upload_stream(
        uploadOptions,
        (error, result) => {
          if (error) {
            reject(error);
          } else {
            resolve(result);
          }
        }
      ).end(fileBuffer);
    });
  } catch (error) {
    throw new Error(`Cloudinary upload failed: ${error.message}`);
  }
};

// Delete file from Cloudinary
const deleteFromCloudinary = async (publicId, resourceType = 'image') => {
  try {
    const result = await cloudinary.uploader.destroy(publicId, {
      resource_type: resourceType
    });
    return result;
  } catch (error) {
    throw new Error(`Cloudinary deletion failed: ${error.message}`);
  }
};

// Generate secure URL with transformations and authentication if needed
const generateSecureUrl = (publicId, options = {}) => {
  try {
    const { resourceType = 'image', ...otherOptions } = options;
    
    // For raw files (like PDFs), generate a signed URL
    if (resourceType === 'raw') {
      const timestamp = Math.round(new Date().getTime() / 1000) + 3600; // 1 hour expiry
      const signature = cloudinary.utils.api_sign_request(
        {
          timestamp,
          public_id: publicId,
          resource_type: resourceType
        },
        process.env.CLOUDINARY_API_SECRET
      );

      return cloudinary.url(publicId, {
        resource_type: resourceType,
        sign_url: true,
        secure: true,
        type: 'upload',
        timestamp,
        signature,
        ...otherOptions
      });
    }

    // For images, use standard secure URL
    return cloudinary.url(publicId, {
      secure: true,
      ...otherOptions
    });
  } catch (error) {
    throw new Error(`URL generation failed: ${error.message}`);
  }
};

module.exports = {
  cloudinary,
  verifyCloudinaryConfig,
  uploadToCloudinary,
  deleteFromCloudinary,
  generateSecureUrl
};
