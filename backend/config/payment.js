require('dotenv').config();

const paymentConfig = {
  platformCommission: parseFloat(process.env.PLATFORM_COMMISSION_RATE || '8'),  // Default 8%
  subscriptionFee: parseInt(process.env.SUBSCRIPTION_FEE || '7000'),  // Default ₦7,000
  
  // Validate settings
  validate() {
    // Validate commission rate
    if (this.platformCommission < 0 || this.platformCommission > 50) {
      throw new Error('Platform commission rate must be between 0 and 50');
    }
    
    // Validate subscription fee
    if (this.subscriptionFee < 1000) {
      throw new Error('Subscription fee must be at least ₦1,000');
    }
  }
};

// Validate settings on load
paymentConfig.validate();

module.exports = paymentConfig; 