# Security Configuration Guide

## Overview

The Etch authentication system now includes environment-based configuration toggles for security features. This allows developers to disable security restrictions during development and testing while ensuring production safety with secure defaults.

## Security Features

### 1. Account Locking (`ENABLE_ACCOUNT_LOCKING`)
- **Purpose**: Prevents brute force attacks on user accounts
- **Behavior**: Locks accounts after 5 failed login attempts for 15 minutes
- **Implementation**: 
  - User/Barber models: `isAccountLocked()` and `incrementFailedLoginAttempts()` methods
  - Controller: Account lock checks in `authController.js`
- **Default**: `true` (enabled for production safety)

### 2. IP Rate Limiting (`ENABLE_IP_RATE_LIMITING`)
- **Purpose**: Prevents distributed brute force attacks
- **Behavior**: Limits login attempts to 5 per IP address per 15 minutes
- **Implementation**: 
  - Middleware: `authRateLimit.checkLoginLimit()` in `middleware/auth.js`
  - Applied to login routes
- **Default**: `true` (enabled for production safety)

### 3. OTP Rate Limiting (`ENABLE_OTP_RATE_LIMITING`)
- **Purpose**: Prevents OTP brute force attacks
- **Behavior**: Limits OTP verification attempts to 5 per 15 minutes (both IP-based and user-based)
- **Implementation**: 
  - IP-based: `authRateLimit.checkOTPLimit()` in `middleware/auth.js`
  - User-based: `validateOTPAttempts()` in `middleware/validation.js`
- **Default**: `true` (enabled for production safety)

## Configuration

### Environment Variables

Add these variables to your `.env` file:

```env
# Security Configuration Toggles
# Set to 'false' during development/testing to disable security restrictions
# Set to 'true' for production deployment (default values for safety)
ENABLE_ACCOUNT_LOCKING=false    # Disable for development
ENABLE_IP_RATE_LIMITING=false   # Disable for development  
ENABLE_OTP_RATE_LIMITING=false  # Disable for development
```

### Default Behavior

- **Missing variables**: Default to `true` (secure)
- **Invalid values**: Default to `true` (secure)
- **Only 'false'**: Explicitly disables the feature
- **Any other value**: Enables the feature

This ensures production safety - security features are enabled unless explicitly disabled.

## Implementation Details

### Files Modified

1. **`config/security.js`** - New security configuration module
2. **`middleware/auth.js`** - Updated rate limiting middleware
3. **`middleware/validation.js`** - Updated OTP validation
4. **`models/User.js`** - Updated account locking methods
5. **`models/Barber.js`** - Updated account locking methods
6. **`server.js`** - Added security configuration logging
7. **`.env`** and **`.env.example`** - Added new environment variables
8. **`README.md`** - Updated documentation

### Security Configuration Module

The `config/security.js` module provides:

- **Environment parsing**: Safe boolean parsing with secure defaults
- **Configuration object**: Centralized security settings
- **Helper functions**: Easy access to security status
- **Logging**: Startup security configuration display
- **Production warnings**: Alerts for disabled features in production

### Testing

Run the security configuration test:

```bash
node test-security-config.js
```

This tests all scenarios:
- All features enabled (production)
- All features disabled (development)
- Mixed configuration
- Invalid values (should default to enabled)
- Missing values (should default to enabled)

## Usage Examples

### Development Setup
```env
# Disable all security features for easy testing
ENABLE_ACCOUNT_LOCKING=false
ENABLE_IP_RATE_LIMITING=false
ENABLE_OTP_RATE_LIMITING=false
```

### Testing Setup
```env
# Enable only specific features for testing
ENABLE_ACCOUNT_LOCKING=true
ENABLE_IP_RATE_LIMITING=false
ENABLE_OTP_RATE_LIMITING=true
```

### Production Setup
```env
# Enable all security features (or omit variables for defaults)
ENABLE_ACCOUNT_LOCKING=true
ENABLE_IP_RATE_LIMITING=true
ENABLE_OTP_RATE_LIMITING=true
```

## Security Considerations

### Production Deployment

1. **Always enable all features** in production
2. **Monitor logs** for security configuration warnings
3. **Verify configuration** during deployment
4. **Test security features** in staging environment

### Development Best Practices

1. **Disable features individually** as needed for testing
2. **Re-enable before committing** to avoid accidental production deployment
3. **Use environment-specific .env files** for different stages
4. **Document any disabled features** in development notes

## Monitoring

The system logs security configuration on startup:

```
🔒 Security Configuration:
   Account Locking: ENABLED
   IP Rate Limiting: ENABLED
   OTP Rate Limiting: ENABLED
   Environment: development
```

Production warnings are displayed if features are disabled:

```
⚠️  WARNING: Security features disabled in production: Account Locking, IP Rate Limiting
```

## Troubleshooting

### Common Issues

1. **Features not disabling**: Check environment variable spelling and values
2. **Unexpected behavior**: Verify .env file is loaded correctly
3. **Production warnings**: Ensure all security features are enabled in production

### Debug Commands

```bash
# Test security configuration
node test-security-config.js

# Check current configuration
node -e "require('./config/security').logSecurityConfig()"

# Verify environment variables
node -e "console.log(process.env.ENABLE_ACCOUNT_LOCKING)"
```

## Future Enhancements

Potential improvements:
- **Rate limit customization**: Configurable attempt limits and time windows
- **Feature-specific logging**: Detailed logs for each security feature
- **Dynamic configuration**: Runtime configuration changes without restart
- **Metrics collection**: Security event monitoring and alerting
