# Admin Authentication System - Implementation Guide

## Overview

The Etch platform now includes a comprehensive Admin Authentication system that provides secure access control for administrative functions. This system follows the same security patterns as user and barber authentication while providing additional admin-specific features.

## Features Implemented

### ✅ Core Authentication Features
- **Admin Model**: Dedicated Admin model with role-based permissions
- **CLI Admin Creation**: Command-line utility for creating admin accounts
- **Secure Login**: JWT-based authentication with security features
- **Password Reset**: Secure password reset workflow with email verification
- **Profile Management**: Admin profile access and management
- **Logout**: Secure logout with session management

### ✅ Security Features
- **Password Hashing**: bcrypt with 12 salt rounds (same as other user types)
- **Account Locking**: 5 failed attempts = 15-minute lock
- **Rate Limiting**: IP-based rate limiting for login attempts
- **JWT Tokens**: Secure token generation with admin-specific claims
- **Token Invalidation**: Force logout on all devices after password reset
- **Security Alerts**: Email notifications for new device logins
- **Audit Trail**: Account creation and activity tracking

### ✅ Admin-Specific Features
- **Role-Based Access**: Admin and Super Admin roles
- **Granular Permissions**: Module-specific permissions (users, barbers, payments, system)
- **Enhanced Security**: Longer OTP expiry (10 minutes vs 5 for users)
- **Extended Login History**: 20 login records vs 10 for regular users
- **Admin Email Templates**: Specialized email templates for admin communications

## File Structure

```
backend/
├── models/
│   └── Admin.js                    # Admin model with security features
├── controllers/
│   └── adminController.js          # Admin authentication controllers
├── routes/
│   └── admin.js                    # Admin authentication routes
├── scripts/
│   └── createAdmin.js              # CLI utility for admin creation
├── middleware/
│   ├── auth.js                     # Updated to handle admin authentication
│   └── validation.js               # Admin validation schemas
├── utils/
│   └── emailService.js             # Admin email templates
└── test-admin-auth.js              # Comprehensive test suite
```

## API Endpoints

### Authentication Endpoints

#### Admin Login
```
POST /api/admin/auth/login
```
**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "AdminPassword123"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "admin": {
      "id": "64f7b1234567890abcdef123",
      "fullName": "Admin User",
      "email": "<EMAIL>",
      "role": "admin",
      "permissions": { ... },
      "lastLogin": "2024-01-01T12:00:00.000Z"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "isNewDevice": false
  }
}
```

#### Get Admin Profile
```
GET /api/admin/auth/profile
Authorization: Bearer <token>
```

#### Admin Logout
```
POST /api/admin/auth/logout
Authorization: Bearer <token>
```

#### Password Reset Request
```
POST /api/admin/auth/forgot-password
```
**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

#### Password Reset Completion
```
POST /api/admin/auth/reset-password
```
**Request Body:**
```json
{
  "token": "reset-token-uuid",
  "newPassword": "NewPassword123",
  "confirmPassword": "NewPassword123"
}
```

### Admin Management Endpoints (Future Implementation)

```
GET    /api/admin/dashboard/stats          # Dashboard statistics
GET    /api/admin/users                    # User management
GET    /api/admin/barbers                  # Barber management
GET    /api/admin/verification-requests    # Pending verifications
POST   /api/admin/barbers/:id/approve      # Approve barber
POST   /api/admin/barbers/:id/reject       # Reject barber
POST   /api/admin/users/:id/suspend        # Suspend user
GET    /api/admin/audit-logs               # Audit logs
```

### Super Admin Only Endpoints (Future Implementation)

```
GET    /api/admin/admins                   # Admin management
POST   /api/admin/admins                   # Create admin
PUT    /api/admin/admins/:id/permissions   # Update permissions
POST   /api/admin/admins/:id/suspend       # Suspend admin
```

## CLI Admin Creation

### Creating the First Admin Account

**⚠️ SECURITY NOTICE: CLI admin creation scripts have been disabled for security reasons after initial setup.**

For emergency admin creation only:

```bash
# 1. Enable CLI admin creation (emergency only)
echo "ENABLE_CLI_ADMIN_CREATION=true" >> .env

# 2. Restart the application
npm restart

# 3. Create admin account
node scripts/createAdminQuick.js "Admin Name" "<EMAIL>" "Password123!" "super_admin"

# 4. IMPORTANT: Disable CLI creation again
# Remove ENABLE_CLI_ADMIN_CREATION=true from .env
# Restart the application
```

**For normal operations, create admin accounts through the admin dashboard interface.**

The script will prompt for:
- Full Name
- Email (with uniqueness validation)
- Password (with strength validation)
- Password Confirmation
- Role Selection (Admin or Super Admin)

### Example CLI Session

```
╔══════════════════════════════════════════════════════════════╗
║                    ETCH ADMIN CREATOR                        ║
║                                                              ║
║  This utility creates admin accounts for the Etch platform  ║
╚══════════════════════════════════════════════════════════════╝

Connecting to database...
✓ Connected to database

Please provide the following information:

Full Name: John Admin
Email: <EMAIL>
Password: ********
Confirm Password: ********

Select admin role:
1. Admin (Standard permissions)
2. Super Admin (Full permissions)
Choice (1 or 2): 2

Admin Account Summary:
Full Name: John Admin
Email: <EMAIL>
Role: Super Admin

Create this admin account? (y/N): y

Creating admin account...

✓ Admin account created successfully!
Admin ID: 64f7b1234567890abcdef123
Email: <EMAIL>
Role: super_admin
Created: 2024-01-01T12:00:00.000Z

The admin can now login at: http://localhost:3000/admin/login

Database connection closed.
```

## Admin Roles and Permissions

### Admin Role (Standard)
```json
{
  "users": {
    "view": true,
    "create": false,
    "edit": true,
    "delete": false,
    "suspend": true
  },
  "barbers": {
    "view": true,
    "approve": true,
    "reject": true,
    "suspend": true,
    "edit": true
  },
  "payments": {
    "view": true,
    "process": true,
    "refund": false
  },
  "system": {
    "settings": false,
    "logs": true,
    "reports": true
  }
}
```

### Super Admin Role
- All permissions set to `true`
- Can manage other admin accounts
- Can modify system settings
- Full access to all platform features

## Security Configuration

The admin authentication system respects the same environment-based security configuration as other user types:

```env
# Security toggles (default: true for production safety)
ENABLE_ACCOUNT_LOCKING=true
ENABLE_IP_RATE_LIMITING=true
ENABLE_OTP_RATE_LIMITING=true
```

## Email Templates

### Admin Security Alert
- Sent when admin logs in from new device
- Includes login details and security recommendations
- Enhanced styling for admin communications

### Admin Password Reset
- Secure reset link with 10-minute expiry
- Admin-specific security warnings
- Clear instructions and security notices

### Admin Password Reset Confirmation
- Confirmation of successful password reset
- Security actions taken summary
- Next steps and security recommendations

## Testing

### Running the Test Suite

```bash
# Ensure server is running
npm start

# In another terminal, run tests
node test-admin-auth.js
```

### Test Coverage

The test suite covers:
- ✅ Admin model creation and validation
- ✅ Admin login functionality
- ✅ Profile access with JWT tokens
- ✅ Invalid login attempt handling
- ✅ Password reset request workflow
- ✅ Password reset completion
- ✅ Admin logout functionality
- ✅ Health check endpoints

## Integration with Existing System

### Middleware Updates
- `auth.js`: Updated to handle admin authentication
- `validation.js`: Added admin-specific validation schemas

### Database Models
- Admin model follows same patterns as User/Barber models
- Compatible with existing PasswordReset model
- Shares security configuration with other user types

### Email Service
- Extended with admin-specific email templates
- Maintains consistency with existing email styling
- Enhanced security messaging for admin communications

## Next Steps

1. **Frontend Integration**: Create admin login/dashboard UI
2. **Admin Dashboard**: Implement dashboard statistics endpoint
3. **User Management**: Build user management functionality
4. **Barber Management**: Implement barber approval/rejection system
5. **Audit Logging**: Add comprehensive audit trail system
6. **Two-Factor Authentication**: Enhance security with 2FA for admins

## Security Best Practices

1. **Regular Password Updates**: Encourage admins to update passwords regularly
2. **Monitor Login Activity**: Review admin login history regularly
3. **Principle of Least Privilege**: Assign minimum required permissions
4. **Secure Environment**: Use strong JWT secrets and secure database connections
5. **Regular Security Audits**: Monitor and review admin activities

## Troubleshooting

### Common Issues

1. **CLI Script Permission Denied**
   ```bash
   chmod +x scripts/createAdmin.js
   ```

2. **Database Connection Issues**
   - Verify MONGODB_URI in .env file
   - Ensure MongoDB is running

3. **Email Configuration**
   - Verify email settings in .env file
   - Check email service configuration

4. **JWT Token Issues**
   - Verify JWT_SECRET in .env file
   - Check token expiration settings

## Conclusion

The Admin Authentication system provides a secure, scalable foundation for administrative access to the Etch platform. It follows established security patterns while providing admin-specific features and enhanced security measures appropriate for administrative users.
