# Password Reset Implementation

## Overview

This document describes the comprehensive password reset workflow implemented for the Etch platform, supporting both <PERSON> and Barbers with robust security features.

## Features Implemented

### 🔐 Security Features
- **10-minute token expiry** for reset links
- **One-time use tokens** that are invalidated after use
- **Rate limiting** to prevent spam (5 minutes between requests)
- **Generic responses** to prevent email enumeration attacks
- **JWT token invalidation** - forces re-login on all devices after password reset
- **Audit logging** for security monitoring
- **Strong password validation** (8+ chars, uppercase, lowercase, number)

### 📧 Email Integration
- **Professional reset emails** with secure links
- **Password change confirmation emails** with security details
- **Responsive HTML templates** with fallback text versions
- **Security alerts** for unauthorized reset attempts

### 🔄 Unified Support
- **Users and Barbers** - single workflow for both user types
- **Account status validation** - only allows reset for valid accounts
- **Registration state checks** - barbers must have completed registration

## API Endpoints

### 1. Request Password Reset
```http
POST /api/auth/forgot-password
Content-Type: application/json

{
  "email": "<EMAIL>"
}
```

**Response (Always 200 for security):**
```json
{
  "success": true,
  "message": "If an account with this email exists, you will receive a password reset link shortly."
}
```

### 2. Complete Password Reset
```http
POST /api/auth/reset-password
Content-Type: application/json

{
  "token": "uuid-token-from-email",
  "newPassword": "NewPassword123",
  "confirmPassword": "NewPassword123"
}
```

**Success Response:**
```json
{
  "success": true,
  "message": "Password reset successfully. Please log in with your new password.",
  "data": {
    "email": "<EMAIL>",
    "passwordChangedAt": "2024-01-15T10:30:00.000Z"
  }
}
```

## Database Schema

### PasswordReset Model
```javascript
{
  email: String,           // User's email address
  token: String,           // Unique reset token (UUID + timestamp)
  userType: String,        // 'user' or 'barber'
  userId: ObjectId,        // Reference to User or Barber
  expiresAt: Date,         // Token expiry (10 minutes)
  used: Boolean,           // Whether token has been used
  usedAt: Date,            // When token was used
  ipAddress: String,       // IP that requested reset
  userAgent: String,       // Browser/device info
  createdAt: Date,         // When token was created
  updatedAt: Date          // Last update
}
```

## Security Workflow

### Password Reset Request
1. **Email Validation** - Check if email exists in User or Barber collections
2. **Account Status Check** - Verify account is in valid state for reset
3. **Rate Limiting** - Prevent multiple requests within 5 minutes
4. **Token Generation** - Create secure UUID + timestamp token
5. **Token Storage** - Save with 10-minute expiry
6. **Email Dispatch** - Send reset link to user
7. **Audit Logging** - Log request for security monitoring
8. **Generic Response** - Always return success to prevent enumeration

### Password Reset Completion
1. **Token Validation** - Verify token exists and hasn't expired
2. **User Lookup** - Find associated user account
3. **Email Verification** - Ensure email matches token
4. **Password Update** - Hash and save new password
5. **Token Invalidation** - Mark token as used
6. **JWT Invalidation** - Force logout on all devices
7. **Cleanup** - Invalidate other reset tokens for user
8. **Confirmation Email** - Send password change notification
9. **Audit Logging** - Log successful reset

## File Structure

```
backend/
├── models/
│   └── PasswordReset.js          # Reset token model
├── controllers/
│   └── authController.js         # Reset controllers (added)
├── routes/
│   └── auth.js                   # Reset routes (added)
├── middleware/
│   ├── auth.js                   # Token invalidation (enhanced)
│   └── validation.js             # Reset validation schemas (added)
├── utils/
│   └── emailService.js           # Reset email templates (added)
└── test-password-reset.js        # Comprehensive tests
```

## Email Templates

### Reset Request Email
- **Subject**: "Password Reset Request - Etch Platform"
- **Content**: Secure reset link with 10-minute expiry warning
- **Security**: Clear instructions and warnings about unauthorized requests

### Password Change Confirmation
- **Subject**: "Password Changed Successfully - Etch Platform"
- **Content**: Change details (IP, device, timestamp)
- **Security**: Instructions for unauthorized change reporting

## Testing

### Automated Tests
```bash
# Run password reset tests
node test-password-reset.js
```

### Manual Testing Checklist
- [ ] Valid email receives reset link
- [ ] Invalid email returns generic success
- [ ] Reset link expires after 10 minutes
- [ ] Token can only be used once
- [ ] Strong password validation works
- [ ] Password mismatch validation works
- [ ] Rate limiting prevents spam
- [ ] JWT tokens invalidated after reset
- [ ] Confirmation email sent
- [ ] Audit logs created

## Environment Variables

Required environment variables:
```env
# Email configuration
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
SUPPORT_EMAIL=<EMAIL>

# Frontend URL for reset links
FRONTEND_URL=https://your-frontend-domain.com

# JWT configuration
JWT_SECRET=your-jwt-secret
JWT_EXPIRE=7d
```

## Security Considerations

### Implemented Protections
- **Email Enumeration Prevention** - Generic responses
- **Token Brute Force Protection** - Short expiry + one-time use
- **Rate Limiting** - Prevents spam attacks
- **Session Invalidation** - Forces re-login after reset
- **Audit Logging** - Security monitoring
- **Strong Password Enforcement** - Prevents weak passwords

### Additional Recommendations
- **IP Geolocation** - Enhanced location tracking in emails
- **CAPTCHA Integration** - Additional bot protection
- **SMS Verification** - Two-factor authentication option
- **Admin Notifications** - Suspicious activity alerts

## Error Handling

### Common Error Responses
```json
// Invalid token
{
  "success": false,
  "message": "Invalid or expired reset token",
  "code": "INVALID_TOKEN"
}

// Validation error
{
  "success": false,
  "message": "Validation failed",
  "errors": [
    {
      "field": "newPassword",
      "message": "Password must contain at least one uppercase letter, one lowercase letter, and one number"
    }
  ]
}

// Rate limit exceeded
{
  "success": false,
  "message": "Too many login attempts from this IP. Please try again in 15 minutes.",
  "code": "IP_RATE_LIMIT"
}
```

## Integration Notes

### Frontend Integration
1. **Reset Request Form** - Simple email input
2. **Reset Completion Form** - Token (from URL) + new password fields
3. **Success/Error Handling** - User-friendly messages
4. **Loading States** - Prevent multiple submissions

### Backend Integration
- **Existing Auth System** - Seamlessly integrated
- **Email Service** - Uses existing Nodemailer setup
- **Database Models** - Compatible with existing User/Barber models
- **Middleware** - Enhanced existing auth middleware

## Deployment Checklist

- [ ] Environment variables configured
- [ ] Email service tested
- [ ] Database indexes created
- [ ] Frontend reset pages implemented
- [ ] Security testing completed
- [ ] Monitoring/logging configured
- [ ] Documentation updated
