# Enhanced Authentication System - Etch Platform

## Overview

The Etch platform now features a comprehensive, unified authentication system that handles login for both **Users** and **Barbers** with advanced security measures, account status management, and multi-device protection.

## 🔐 Key Features

### 1. **Unified Login System**
- Single `/api/auth/login` endpoint for both users and barbers
- Automatic detection of user type based on email
- Role-based authentication and authorization

### 2. **Advanced Security Measures**
- **Account Locking**: 5 failed attempts = 15-minute lockout
- **IP Rate Limiting**: 5 attempts per IP per 15 minutes
- **OTP Rate Limiting**: 5 OTP attempts per 15 minutes
- **Multi-Device Detection**: Security alerts for new device logins
- **Failed Login Tracking**: Per-account attempt monitoring

### 3. **Comprehensive Status Checks**
- **Users**: `pending_verification`, `verified`, `suspended`, `inactive`
- **Barbers**: `step1_completed`, `step2_completed`, `step3_completed`, `pending_verification`, `verified`, `rejected`, `suspended`

### 4. **Enhanced Error Handling**
- Specific error codes for different scenarios
- Detailed error messages with actionable information
- Security-conscious error responses

## 🚀 API Endpoints

### Login Flow

#### 1. **POST** `/api/auth/login`
Unified login endpoint for users and barbers.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "UserPass123"
}
```

**Success Response:**
```json
{
  "success": true,
  "message": "Login verification code sent to your email",
  "data": {
    "userId": "user_id",
    "email": "<EMAIL>",
    "role": "user",
    "userType": "user",
    "otpSent": true,
    "otpExpiry": "2024-01-01T12:05:00.000Z"
  }
}
```

**Error Responses:**
- `EMAIL_NOT_FOUND`: Email doesn't exist
- `INVALID_PASSWORD`: Wrong password
- `ACCOUNT_LOCKED`: Too many failed attempts
- `ACCOUNT_SUSPENDED`: Account suspended
- `REGISTRATION_INCOMPLETE`: Barber hasn't completed registration
- `IP_RATE_LIMIT`: Too many requests from IP

#### 2. **POST** `/api/auth/verify-login-otp`
Verify OTP and complete login (unified for users and barbers).

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "otp": "123456"
}
```

**Success Response:**
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "user": {
      "id": "user_id",
      "fullName": "John Doe",
      "email": "<EMAIL>",
      "role": "user",
      "status": "verified"
    },
    "token": "jwt_token_here",
    "expiresIn": "7d",
    "userType": "user",
    "isNewDevice": false
  }
}
```

## 🛡️ Security Features

### Account Locking Mechanism
```javascript
// After 5 failed login attempts
{
  "success": false,
  "message": "Account is temporarily locked due to multiple failed login attempts. Try again in 14 minutes.",
  "code": "ACCOUNT_LOCKED",
  "lockTimeRemaining": 14
}
```

### Multi-Device Security Alerts
When a user logs in from a new device:
- Security alert email is sent automatically
- Login history tracks device information
- `isNewDevice: true` flag in response

### Rate Limiting
- **IP-based**: 5 login attempts per 15 minutes per IP
- **OTP-based**: 5 OTP attempts per 15 minutes per IP
- **Account-based**: 5 failed password attempts per account

## 📊 Account Status Handling

### User Account States
| Status | Description | Login Behavior |
|--------|-------------|----------------|
| `pending_verification` | Email not verified | Resends OTP for verification |
| `verified` | Active account | Sends login OTP |
| `suspended` | Account suspended | Login blocked |
| `inactive` | Account deactivated | Login blocked |

### Barber Account States
| Status | Description | Login Behavior |
|--------|-------------|----------------|
| `step1_completed` | Personal info only | Registration incomplete error |
| `step2_completed` | Documents uploaded | Registration incomplete error |
| `step3_completed` | Password set | Registration incomplete error |
| `pending_verification` | Awaiting admin approval | Shows "under review" message |
| `verified` | Approved by admin | Sends login OTP |
| `rejected` | Application rejected | Login blocked |
| `suspended` | Account suspended | Login blocked |

## 🔧 Database Schema Updates

### User Model Enhancements
```javascript
security: {
  failedLoginAttempts: Number,
  accountLockedUntil: Date,
  lastFailedLogin: Date,
  passwordChangedAt: Date,
  suspiciousActivityCount: Number,
  lastSuspiciousActivity: Date
},
loginHistory: [{
  loginAt: Date,
  ipAddress: String,
  userAgent: String,
  deviceInfo: String,
  location: String,
  isNewDevice: Boolean
}]
```

### Barber Model Enhancements
```javascript
// Same security and loginHistory fields as User model
security: { /* ... */ },
loginHistory: [{ /* ... */ }]
```

## 📧 Email Notifications

### Security Alert Email
Sent automatically when login detected from new device:
- Login timestamp and location
- Device/browser information
- Security recommendations
- Quick action buttons

### Enhanced Login OTP Email
- Clear security warnings
- Expiration time display
- Suspicious activity guidance

## 🧪 Testing

Run the comprehensive test suite:
```bash
node test-enhanced-auth.js
```

**Test Coverage:**
- User registration and login
- Barber login scenarios
- Account locking mechanism
- Rate limiting functionality
- Invalid login attempts
- OTP verification
- Error code validation

## 🔄 Migration Guide

### For Existing Users
- No action required - existing accounts work seamlessly
- Security fields are automatically initialized
- Login flow remains the same

### For Existing Barbers
- Verified barbers can login immediately
- Pending barbers see appropriate status messages
- Failed login tracking starts from first login attempt

## 🚨 Security Best Practices

1. **Monitor Failed Attempts**: Track patterns in failed login attempts
2. **Review Security Alerts**: Investigate suspicious login activities
3. **Regular Password Updates**: Encourage users to update passwords
4. **Device Management**: Help users identify and manage trusted devices
5. **Account Recovery**: Provide secure account recovery mechanisms

## 📈 Performance Considerations

- **In-Memory Rate Limiting**: Uses Map for fast IP tracking
- **Database Optimization**: Indexed security fields for quick lookups
- **Email Queue**: Asynchronous email sending to prevent blocking
- **Token Management**: Efficient JWT generation and validation

## 🔮 Future Enhancements

- **Two-Factor Authentication (2FA)**: SMS or app-based 2FA
- **Device Fingerprinting**: Advanced device identification
- **Geolocation Tracking**: IP-based location detection
- **Behavioral Analysis**: Login pattern analysis
- **Session Management**: Advanced session control and monitoring
