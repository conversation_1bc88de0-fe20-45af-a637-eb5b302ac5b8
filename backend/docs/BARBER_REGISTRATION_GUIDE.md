# Barber Registration System - Implementation Guide

## Overview

This document describes the comprehensive 3-step barber registration system implemented for the Etch platform. The system ensures secure, validated, and admin-reviewed barber onboarding with proper document verification and email confirmation.

## Architecture

### Database Models

1. **Barber Model** (`models/Barber.js`)
   - Personal information (Step 1)
   - Business information and documents (Step 2)
   - Security credentials (Step 3)
   - Registration status tracking
   - OTP verification system

2. **VerificationRequest Model** (`models/VerificationRequest.js`)
   - Admin verification queue management
   - SLA tracking (48-hour deadline)
   - Document verification status
   - Audit trail and communication logs

### Key Features

- ✅ Email uniqueness across all user types (users, barbers, admins)
- ✅ 3-step registration process with status tracking
- ✅ Secure file upload to Cloudinary with validation
- ✅ Password hashing with bcrypt (12 salt rounds)
- ✅ 6-digit OTP with 5-minute expiration
- ✅ Admin notification system
- ✅ SLA management for verification requests
- ✅ Comprehensive email templates
- ✅ File type and size validation
- ✅ Error handling and rollback mechanisms

## API Endpoints

### Step 1: Personal Information
```
POST /api/auth/register/barber/step1
```

**Request Body:**
```json
{
  "fullName": "<PERSON>",
  "email": "<EMAIL>",
  "phoneNumber": "+2348012345678",
  "address": "123 Main Street, Lagos, Nigeria"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Step 1 completed successfully...",
  "data": {
    "barberId": "64f7b1234567890abcdef123",
    "fullName": "John Doe",
    "email": "<EMAIL>",
    "status": "step1_completed",
    "nextStep": "step2",
    "nextStepUrl": "/api/auth/register/barber/step2"
  }
}
```

### Step 2: Business Documentation
```
POST /api/auth/register/barber/step2
Content-Type: multipart/form-data
```

**Form Data:**
- `barberId`: string (required)
- `businessName`: string (required)
- `cacCertificate`: file (PDF/image, max 5MB)
- `ninDocument`: file (PDF/image, max 5MB)
- `passportPhoto`: file (image only, max 5MB)

**Response:**
```json
{
  "success": true,
  "message": "Step 2 completed successfully...",
  "data": {
    "barberId": "64f7b1234567890abcdef123",
    "businessName": "John's Barber Shop",
    "documents": {
      "cacCertificate": "https://cloudinary.com/...",
      "ninDocument": "https://cloudinary.com/...",
      "passportPhoto": "https://cloudinary.com/..."
    },
    "status": "step2_completed",
    "nextStep": "step3"
  }
}
```

### Step 3: Security Setup
```
POST /api/auth/register/barber/step3
```

**Request Body:**
```json
{
  "barberId": "64f7b1234567890abcdef123",
  "password": "SecurePassword123",
  "confirmPassword": "SecurePassword123"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Registration completed successfully...",
  "data": {
    "barberId": "64f7b1234567890abcdef123",
    "email": "<EMAIL>",
    "status": "step3_completed",
    "otpSent": true,
    "otpExpiry": "2024-01-01T12:05:00.000Z",
    "nextStep": "verify_email"
  }
}
```

### OTP Verification
```
POST /api/auth/register/barber/verify-otp
```

**Request Body:**
```json
{
  "barberId": "64f7b1234567890abcdef123",
  "otp": "123456"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Email verified successfully...",
  "data": {
    "barberId": "64f7b1234567890abcdef123",
    "status": "pending_verification",
    "verificationRequestId": "64f7b1234567890abcdef456",
    "estimatedVerificationTime": "24-48 hours"
  }
}
```

## Validation Rules

### Personal Information (Step 1)
- **Full Name**: 2-100 characters, letters and spaces only
- **Email**: Valid email format, unique across all user types
- **Phone Number**: Nigerian format (+234, 234, or 0 prefix)
- **Address**: 10-500 characters

### Business Information (Step 2)
- **Business Name**: 3-100 characters
- **CAC Certificate**: PDF/image, max 5MB
- **NIN Document**: PDF/image, max 5MB
- **Passport Photo**: Image only, max 5MB, auto-cropped to 300x300

### Security Setup (Step 3)
- **Password**: Min 8 characters, uppercase + lowercase + number
- **Confirm Password**: Must match password

## File Upload Security

### Supported File Types
- **Documents**: PDF, JPG, PNG
- **Passport Photo**: JPG, PNG only

### Security Measures
- File type validation (MIME type + extension)
- File size limits (5MB max)
- Virus scanning (via Cloudinary)
- Secure cloud storage with organized folder structure
- Automatic image optimization and transformation

## Email System

### Templates Implemented
1. **Barber OTP Email**: Email verification with 6-digit code
2. **Registration Confirmation**: Success notification to barber
3. **Admin Notification**: New verification request alert

### Email Features
- HTML and text versions
- Professional styling
- Security warnings
- Clear call-to-action buttons
- Support contact information

## Admin Verification System

### Verification Request Features
- Auto-assignment to available admins
- 48-hour SLA with reminder system
- Document verification tracking
- Audit trail for all actions
- Communication log

### Admin Dashboard Integration
- Pending verification queue
- Document review interface
- Approval/rejection workflow
- Bulk operations support

## Testing

### Test Script
Run the test script to verify the implementation:

```bash
cd backend
node test-barber-registration.js
```

### Manual Testing
1. Start the server: `npm run dev`
2. Use Postman or similar tool to test each endpoint
3. Verify email delivery (check email service configuration)
4. Test file uploads with actual documents

## Environment Variables Required

```env
# Cloudinary Configuration
CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret

# Email Configuration
EMAIL_HOST=your_email_host
EMAIL_USER=your_email_user
EMAIL_PASSWORD=your_email_password
SUPPORT_EMAIL=<EMAIL>

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_PANEL_URL=https://admin.etch.ng
```

## Error Handling

### Common Error Scenarios
1. **Email Already Exists**: Returns 400 with specific error message
2. **Invalid File Types**: Returns 400 with file validation errors
3. **Missing Documents**: Returns 400 with missing file errors
4. **Invalid OTP**: Returns 400 with OTP validation errors
5. **Server Errors**: Returns 500 with generic error message

### Rollback Mechanisms
- Failed uploads are automatically cleaned up
- Database transactions ensure data consistency
- Email failures don't block registration progress

## Security Considerations

1. **Password Security**: bcrypt with 12 salt rounds
2. **File Security**: Type validation, size limits, virus scanning
3. **Email Security**: OTP expiration, attempt limiting
4. **Data Privacy**: Sensitive data excluded from API responses
5. **Rate Limiting**: Built-in protection against abuse

## Next Steps

1. Implement admin dashboard for verification management
2. Add SMS verification as backup to email
3. Implement document OCR for automated validation
4. Add real-time notifications for status updates
5. Create barber profile setup after verification
