# Environment Configuration Guide

This guide explains how to configure environment variables for different deployment environments (development, staging, production) with proper Monnify payment integration.

## Environment Detection

The application automatically detects the environment using the `NODE_ENV` variable and configures Monnify base URLs accordingly:

- **Development**: `NODE_ENV=development` → Uses `https://sandbox.monnify.com`
- **Production**: `NODE_ENV=production` → Uses `https://api.monnify.com`

## Required Environment Variables

### Core Application
```bash
NODE_ENV=development  # Set to 'production' for live environment
PORT=5000
DATABASE_URL=mongodb://localhost:27017/etch-barber-app
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=7d
CLIENT_URL=http://localhost:5173
FRONTEND_URL=http://localhost:5173
```

### Monnify Payment Configuration

**IMPORTANT**: Use `MONNIFY_` prefix for all Monnify environment variables.

```bash
# Monnify API Credentials
MONNIFY_API_KEY=MK_TEST_YOUR_API_KEY_HERE
MONNIFY_SECRET_KEY=YOUR_SECRET_KEY_HERE
MONNIFY_CONTRACT_CODE=YOUR_CONTRACT_CODE_HERE

# Optional: Override base URL (for testing purposes only)
# MONNIFY_BASE_URL=https://sandbox.monnify.com
```

### Email Configuration
```bash
EMAIL_PROVIDER=resend
RESEND_API_KEY=your-resend-api-key-here
```

### File Upload Configuration
```bash
CLOUDINARY_CLOUD_NAME=your-cloudinary-cloud-name
CLOUDINARY_API_KEY=your-cloudinary-api-key
CLOUDINARY_API_SECRET=your-cloudinary-api-secret
```

### Security Configuration
```bash
BCRYPT_SALT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=3600000
RATE_LIMIT_MAX=100
ADMIN_MASTER_PASSWORD=your-super-secure-admin-password-here
```

## Environment-Specific Setup

### Development Environment
1. Set `NODE_ENV=development`
2. Use Monnify sandbox credentials (MK_TEST_*)
3. Base URL will automatically be set to `https://sandbox.monnify.com`

### Production Environment
1. Set `NODE_ENV=production`
2. Use Monnify live credentials (MK_PROD_*)
3. Base URL will automatically be set to `https://api.monnify.com`
4. Ensure all other production settings are configured

## Monnify Environment Mapping

| Environment | NODE_ENV | Base URL | Credentials |
|-------------|----------|----------|-------------|
| Development | development | https://sandbox.monnify.com | MK_TEST_* |
| Production | production | https://api.monnify.com | MK_PROD_* |

## Setup Instructions

### 1. Development Setup
```bash
cp .env.example .env
# Edit .env with your development credentials
NODE_ENV=development
MONNIFY_API_KEY=MK_TEST_YOUR_SANDBOX_KEY
# ... other variables
```

### 2. Production Setup
```bash
# Set production environment variables
NODE_ENV=production
MONNIFY_API_KEY=MK_PROD_YOUR_LIVE_KEY
MONNIFY_SECRET_KEY=YOUR_LIVE_SECRET_KEY
# ... other production variables
```

## Environment Variable Priority

The payment service uses the following priority for base URL selection:
1. `MONNIFY_BASE_URL` (if explicitly set)
2. Auto-detection based on `NODE_ENV`:
   - `production` → `https://api.monnify.com`
   - `development` (or any other value) → `https://sandbox.monnify.com`

## Troubleshooting

### Common Issues
1. **Wrong base URL**: Ensure `NODE_ENV` is set correctly
2. **Authentication errors**: Verify credentials match the environment
3. **Sandbox vs Live**: Make sure to use test credentials for development and live credentials for production

### Verification
The payment service logs its configuration on startup:
```
PaymentService initialized with: {
  environment: 'development',
  isProduction: false,
  baseUrl: 'https://sandbox.monnify.com',
  hasApiKey: true,
  hasSecretKey: true,
  hasContractCode: true
}
```

## Frontend Environment Configuration

The frontend also requires environment configuration for API endpoints.

### Frontend Environment Variables

Create a `.env` file in the `frontend/` directory:

```bash
# Development
VITE_API_URL=http://localhost:5000/api

# Production
# VITE_API_URL=https://your-production-backend.com/api

# Staging
# VITE_API_URL=https://your-staging-backend.com/api
```

### Frontend Build Process

For production builds, ensure the environment variables are set:
```bash
# Build for production
VITE_API_URL=https://your-production-backend.com/api npm run build
```

## Security Best Practices

1. Never commit `.env` files to version control
2. Use different credentials for each environment
3. Regularly rotate API keys and secrets
4. Monitor API usage and set up alerts
5. Use secure connection (HTTPS) in production
6. Keep frontend and backend environment configurations in sync
7. Use environment-specific URLs and credentials 