const User = require('../models/User');
const Barber = require('../models/Barber');
const PasswordReset = require('../models/PasswordReset');
const { generateToken, authRateLimit, invalidateUserTokens } = require('../middleware/auth');
const { validateOTPAttempts } = require('../middleware/validation');
const emailService = require('../utils/emailService');
const { MAX_LOGIN_ATTEMPTS, LOCK_TIME } = require('../config/security');
const { catchAsync, AppError } = require('../utils/errorHandlers');

// Convert lock time from minutes to milliseconds
const ACCOUNT_LOCK_DURATION = LOCK_TIME * 60 * 1000;

// User Registration
exports.registerUser = catchAsync(async (req, res, next) => {
  const { fullName, email, password } = req.body;

  // Check if email already exists in both users and barbers collections
  const existingUser = await User.findByEmail(email);
  const existingBarber = await Barber.findByEmail(email);
  
  if (existingUser || existingBarber) {
    return next(new AppError('Email is already registered', 400));
  }

  // Create new user
  const user = new User({
    fullName,
    email,
    password, // Will be hashed by pre-save middleware
    role: 'user',
    status: 'pending_verification'
  });

  // Generate OTP
  const otp = user.generateOTP();

  // Save user to database
  await user.save();

  // Send OTP email
  try {
    await emailService.sendOTPEmail(email, otp, fullName);
  } catch (emailError) {
    console.error('Failed to send OTP email:', emailError);
    // Don't fail registration if email fails, but log it
    // User can request OTP resend
  }

  // Return success response
  res.status(201).json({
    success: true,
    message: 'User registered successfully. Please check your email for OTP verification.',
    data: {
      userId: user._id,
      email: user.email,
      fullName: user.fullName,
      status: user.status,
      otpSent: true,
      otpExpiry: user.otp.expiresAt
    }
  });
});

// OTP Verification
exports.verifyOTP = catchAsync(async (req, res, next) => {
  const { email, otp, isEmailVerification = true } = req.body;

  // Find user by email
  const user = await User.findByEmail(email);
  if (!user) {
    return next(new AppError('User not found', 404));
  }

  // Check rate limiting for OTP attempts
  const rateLimitCheck = validateOTPAttempts(user);
  if (!rateLimitCheck.valid) {
    return next(new AppError(rateLimitCheck.message, 429));
  }

  // Verify OTP
  const otpResult = user.verifyOTP(otp);
  
  if (!otpResult.success) {
    await user.save(); // Save updated attempt count
    return next(new AppError(otpResult.message, 400));
  }

  // Update user status to verified
  user.status = 'verified';
  user.emailVerifiedAt = new Date();
  await user.save();

  // For email verification, don't generate token
  if (isEmailVerification) {
    // Send welcome email
    try {
      await emailService.sendWelcomeEmail(email, user.fullName);
    } catch (emailError) {
      console.error('Failed to send welcome email:', emailError);
      // Don't fail verification if welcome email fails
    }

    // Return success without token
    return res.status(200).json({
      success: true,
      message: 'Email verified successfully. Please log in to continue.',
      data: {
        verified: true,
        requiresLogin: true
      }
    });
  }

  // For login verification, generate token
  const token = generateToken(user._id, user.role);

  // Return success response with token
  res.status(200).json({
    success: true,
    message: 'Email verified successfully. Welcome to Etch!',
    data: {
      user: user.fullProfile,
      token: token,
      expiresIn: '7d'
    }
  });
});

// Unified Login for Users and Barbers
exports.login = catchAsync(async (req, res, next) => {
  const { email, password, userType } = req.body;
  
  // Find user by email based on userType
  let user;
  if (userType === 'barber') {
    user = await Barber.findByEmail(email);
  } else {
    user = await User.findByEmail(email);
  }

  // Always return the same error for invalid credentials
  // This prevents user enumeration
  if (!user) {
    return next(new AppError('Invalid email or password', 401));
  }

  // Verify password first before revealing any account information
  try {
    const isPasswordValid = await user.comparePassword(password);

    if (!isPasswordValid) {
      // Increment failed attempts but don't reveal this information
      user.security.failedLoginAttempts += 1;
      user.security.lastFailedLogin = new Date();

      // Save the updated attempts silently
      await user.save();

      // Return the same generic error
      return next(new AppError('Invalid email or password', 401));
    }

    // Password is valid - NOW we can check account status
    // Check if account is locked (only after valid credentials)
    if (user.security.accountLockedUntil && user.security.accountLockedUntil > new Date()) {
      const remainingTime = Math.ceil((user.security.accountLockedUntil - new Date()) / 1000 / 60);
      return next(new AppError(`Account is locked. Please try again in ${remainingTime} minutes`, 403));
    }

    // Check if too many failed attempts (only after valid credentials)
    if (user.security.failedLoginAttempts >= MAX_LOGIN_ATTEMPTS) {
      user.security.accountLockedUntil = new Date(Date.now() + ACCOUNT_LOCK_DURATION);
      await user.save();
      return next(new AppError(`Too many failed attempts. Account locked for ${LOCK_TIME} minutes`, 403));
    }

    // Now check other account statuses
    if (userType === 'barber') {
      // For barbers, check both status and registrationStatus for banned/suspended accounts
      if (user.status === 'banned') {
        return next(new AppError('Your account has been banned. Please contact support for assistance.', 403));
      }

      if (user.status === 'suspended') {
        return next(new AppError('Your account has been suspended. Please contact support for assistance.', 403));
      }

      if (user.registrationStatus === 'suspended') {
        return next(new AppError('Your account has been suspended. Please contact support for assistance.', 403));
      }

      if (user.registrationStatus === 'banned') {
        return next(new AppError('Your account has been banned. Please contact support for assistance.', 403));
      }

      if (user.registrationStatus === 'pending_verification') {
        return next(new AppError('Your barber account is pending approval. We will notify you once approved.', 401));
      }

      if (user.registrationStatus !== 'verified') {
        return next(new AppError('Please complete your registration process.', 401));
      }
    } else {
      // For regular users, check status
      if (user.status === 'suspended') {
        return next(new AppError('Your account has been suspended. Please contact support for assistance.', 403));
      }

      if (user.status === 'pending_verification') {
        return next(new AppError('Please verify your email address before logging in.', 401));
      }

      if (user.status !== 'verified' && user.status !== 'active') {
        return next(new AppError(`Account status is ${user.status}. Please contact support if you believe this is an error.`, 401));
      }
    }

    // Check verification status for barbers
    if (userType === 'barber') {
      if (user.registrationStatus === 'pending_approval') {
        // Send reminder email to admin
        try {
          await emailService.sendAdminPendingBarberLoginReminder({
            fullName: user.fullName,
            email: user.email,
            businessName: user.businessName
          });
        } catch (emailError) {
          console.error('Failed to send admin reminder email:', emailError);
          // Don't block the login response if email fails
        }

        return res.status(200).json({
          success: true,
          data: {
            user: user.fullProfile,
            requiresApproval: true
          }
        });
      }

      if (user.registrationStatus !== 'verified') {
        return next(new AppError('Please complete your registration process.', 401));
      }
    }

    // Reset failed login attempts on successful login
    user.security.failedLoginAttempts = 0;
    user.security.accountLockedUntil = null;
    user.lastLogin = new Date();
    await user.save();

    // Generate token
    const token = generateToken(user._id, userType);

    // Return success response with user role
    res.status(200).json({
      success: true,
      message: 'Login successful',
      data: {
        user: {
          ...user.fullProfile,
          role: userType // Explicitly set the role
        },
        token,
        expiresIn: '7d'
      }
    });

  } catch (error) {
    return next(new AppError('An error occurred during login', 500));
  }
});

// Unified Verify Login OTP for Users and Barbers
const verifyLoginOTP = async (req, res) => {
  try {
    const { email, otp } = req.body;
    const ipAddress = req.ip || req.connection.remoteAddress;
    const userAgent = req.get('User-Agent') || 'Unknown';

    // Find user in both User and Barber collections
    let user = await User.findByEmail(email);
    let userType = 'user';

    if (!user) {
      user = await Barber.findByEmail(email);
      userType = 'barber';
    }

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found',
        code: 'USER_NOT_FOUND'
      });
    }

    // Check account status based on user type
    if (userType === 'user') {
      if (user.status === 'banned' || user.status === 'suspended') {
        return res.status(403).json({
          success: false,
          message: `Your account has been ${user.status}. Please contact support for assistance.`,
          code: 'ACCOUNT_RESTRICTED'
        });
      }

      if (user.status !== 'verified') {
        return res.status(400).json({
          success: false,
          message: 'Account not verified. Please verify your email first.',
          code: 'ACCOUNT_NOT_VERIFIED'
        });
      }
    } else if (userType === 'barber') {
      // Check both status and registrationStatus for barbers
      if (user.status === 'banned' || user.status === 'suspended' || 
          user.registrationStatus === 'banned' || user.registrationStatus === 'suspended') {
        return res.status(403).json({
          success: false,
          message: 'Your account has been restricted. Please contact support for assistance.',
          code: 'ACCOUNT_RESTRICTED'
        });
      }

      if (user.registrationStatus !== 'verified') {
        return res.status(400).json({
          success: false,
          message: 'Account not verified. Please complete registration process.',
          code: 'ACCOUNT_NOT_VERIFIED'
        });
      }
    }

    // Check if account is locked
    if (user.isAccountLocked()) {
      const lockTime = Math.ceil((user.security.accountLockedUntil - new Date()) / 60000);
      return res.status(423).json({
        success: false,
        message: `Account is temporarily locked. Try again in ${lockTime} minutes.`,
        code: 'ACCOUNT_LOCKED',
        lockTimeRemaining: lockTime
      });
    }

    // Check rate limiting for OTP attempts
    const rateLimitCheck = validateOTPAttempts(user);
    if (!rateLimitCheck.valid) {
      return res.status(429).json({
        success: false,
        message: rateLimitCheck.message,
        code: 'OTP_RATE_LIMIT'
      });
    }

    // Verify OTP
    const otpResult = user.verifyOTP(otp);

    if (!otpResult.success) {
      await user.save(); // Save updated attempt count
      return res.status(400).json({
        success: false,
        message: otpResult.message,
        code: 'INVALID_OTP'
      });
    }

    // Reset failed login attempts on successful OTP verification
    user.resetFailedLoginAttempts();

    // Add to login history and check for new device
    const isNewDevice = user.addLoginHistory(ipAddress, userAgent);

    await user.save();

    // Send security alert for new device login
    if (isNewDevice) {
      try {
        await emailService.sendSecurityAlertEmail(user.email, user.fullName, {
          ipAddress,
          userAgent,
          loginTime: new Date(),
          location: 'Unknown' // Could be enhanced with IP geolocation
        });
      } catch (emailError) {
        console.error('Failed to send security alert email:', emailError);
        // Don't fail login if email fails
      }
    }

    // Generate JWT token with appropriate role
    const role = userType === 'barber' ? 'barber' : user.role;
    const token = generateToken(user._id, role);

    // Return success response with token
    res.status(200).json({
      success: true,
      message: 'Login successful',
      data: {
        user: user.fullProfile,
        token: token,
        expiresIn: '7d',
        userType: userType,
        isNewDevice: isNewDevice
      }
    });

  } catch (error) {
    console.error('Login OTP verification error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error during login verification'
    });
  }
};

// Resend OTP
exports.resendOTP = catchAsync(async (req, res, next) => {
  const { email } = req.body;

  // Find user by email
  const user = await User.findByEmail(email);
  if (!user) {
    return next(new AppError('User not found', 404));
  }

  // Check if resend is allowed
  if (user.otp.lastAttempt && Date.now() - user.otp.lastAttempt < 60000) {
    return next(new AppError('Please wait 1 minute before requesting a new OTP', 429));
  }

  // Generate new OTP
  const otp = user.generateOTP();
  await user.save();

  // Send OTP email
  await emailService.sendOTPEmail(user.email, otp, user.fullName);

  res.status(200).json({
    success: true,
    message: 'New OTP has been sent to your email',
    data: {
      email: user.email,
      otpExpiry: user.otp.expiresAt
    }
  });
});

// Get Profile
exports.getProfile = catchAsync(async (req, res, next) => {
  // Check user type from token
  const userRole = req.user.role;
  let user;

  if (userRole === 'barber') {
    user = await Barber.findById(req.user._id).select('-password -otp');
  } else {
    user = await User.findById(req.user._id).select('-password -otp');
  }

  if (!user) {
    return next(new AppError('User not found', 404));
  }

  // Add role to response
  const userData = user.toObject();
  userData.role = userRole;

  res.status(200).json({
    success: true,
    data: {
      user: userData
    }
  });
});

// Logout
exports.logout = catchAsync(async (req, res) => {
  // Invalidate user's tokens
  await invalidateUserTokens(req.user._id);

  res.status(200).json({
    success: true,
    message: 'Logged out successfully'
  });
});

// Request Password Reset
exports.requestPasswordReset = catchAsync(async (req, res, next) => {
  const { email } = req.body;

  // Find user by email
  const user = await User.findByEmail(email);
  if (!user) {
    return next(new AppError('User not found', 404));
  }

  // Generate password reset token
  const resetToken = await PasswordReset.createResetToken(user._id);

  // Send password reset email
  await emailService.sendPasswordResetEmail(
    user.email,
    user.fullName,
    resetToken.token
  );

  res.status(200).json({
    success: true,
    message: 'Password reset instructions sent to your email'
  });
});

// Reset Password
exports.resetPassword = catchAsync(async (req, res, next) => {
  const { token, password } = req.body;

  // Find valid reset token
  const resetToken = await PasswordReset.findOne({
    token,
    expiresAt: { $gt: new Date() },
    used: false
  });

  if (!resetToken) {
    return next(new AppError('Invalid or expired reset token', 400));
  }

  // Find user and update password
  const user = await User.findById(resetToken.userId);
  if (!user) {
    return next(new AppError('User not found', 404));
  }

  // Update password and invalidate all existing sessions
  user.password = password;
  await user.save();
  await invalidateUserTokens(user._id);

  // Mark reset token as used
  resetToken.used = true;
  resetToken.usedAt = new Date();
  await resetToken.save();

  // Send password change confirmation email
  await emailService.sendPasswordChangeConfirmation(user.email, user.fullName);

  res.status(200).json({
    success: true,
    message: 'Password reset successful. Please log in with your new password.'
  });
});

module.exports = {
  registerUser: exports.registerUser,
  verifyOTP: exports.verifyOTP,
  login: exports.login,
  verifyLoginOTP: exports.verifyLoginOTP,
  resendOTP: exports.resendOTP,
  getProfile: exports.getProfile,
  logout: exports.logout,
  requestPasswordReset: exports.requestPasswordReset,
  resetPassword: exports.resetPassword
};
