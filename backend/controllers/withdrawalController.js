const Withdrawal = require('../models/Withdrawal');
const BarberEarnings = require('../models/BarberEarnings');
const Barber = require('../models/Barber');
const paymentService = require('../utils/paymentService');
const { sendEmail } = require('../utils/emailService');

// Get barber's earnings and balance information
exports.getBarberEarnings = async (req, res) => {
  try {
    const earnings = await BarberEarnings.getOrCreate(req.user._id);
    
    // Calculate pending withdrawals (withdrawals that are still pending approval)
    const pendingWithdrawals = earnings.transactions
      .filter(t => t.type === 'WITHDRAWAL' && t.description.includes('(PENDING)'))
      .reduce((total, t) => total + Math.abs(t.amount), 0);
    
    // Calculate actual available balance after subtracting pending withdrawals
    const actualAvailableBalance = earnings.availableBalance - pendingWithdrawals;
    
    res.status(200).json({
      success: true,
      data: {
        availableBalance: earnings.availableBalance,
        actualAvailableBalance: Math.max(0, actualAvailableBalance), // Never go below 0
        pendingWithdrawals: pendingWithdrawals,
        pendingBalance: earnings.pendingBalance,
        totalEarnings: earnings.totalEarnings,
        totalWithdrawn: earnings.totalWithdrawn,
        lastWithdrawalDate: earnings.lastWithdrawalDate
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error fetching earnings information',
      error: error.message
    });
  }
};

// Get list of Nigerian banks from Monnify
exports.getBankList = async (req, res) => {
  try {
    const bankListResult = await paymentService.getBankList();
    
    if (bankListResult.success) {
      res.status(200).json({
        success: true,
        data: {
          banks: bankListResult.banks
        }
      });
    } else {
      res.status(500).json({
        success: false,
        message: 'Failed to fetch bank list',
        error: bankListResult.error
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error fetching bank list',
      error: error.message
    });
  }
};

// Verify bank account details
exports.verifyBankAccount = async (req, res) => {
  try {
    const { bankCode, accountNumber } = req.body;

    if (!bankCode || !accountNumber) {
      return res.status(400).json({
        success: false,
        message: 'Bank code and account number are required'
      });
    }

    // Validate account number format
    if (!/^\d{10}$/.test(accountNumber)) {
      return res.status(400).json({
        success: false,
        message: 'Account number must be exactly 10 digits'
      });
    }

    const verificationResult = await paymentService.verifyBankAccount(bankCode, accountNumber);
    
    if (verificationResult.success) {
      res.status(200).json({
        success: true,
        data: {
          accountName: verificationResult.accountName,
          accountNumber: verificationResult.accountNumber,
          bankCode: verificationResult.bankCode
        }
      });
    } else {
      res.status(400).json({
        success: false,
        message: 'Account verification failed',
        error: verificationResult.error
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error verifying bank account',
      error: error.message
    });
  }
};

// Get barber's transaction history
exports.getTransactionHistory = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const startDate = req.query.startDate ? new Date(req.query.startDate) : null;
    const endDate = req.query.endDate ? new Date(req.query.endDate) : null;
    const type = req.query.type;

    const earnings = await BarberEarnings.findOne({ barber: req.user._id });
    if (!earnings) {
      return res.status(404).json({
        success: false,
        message: 'No earnings record found'
      });
    }

    let query = {};
    if (startDate && endDate) {
      query.createdAt = { $gte: startDate, $lte: endDate };
    }
    if (type) {
      query.type = type;
    }

    const transactions = earnings.transactions
      .filter(t => {
        if (query.createdAt) {
          return t.createdAt >= query.createdAt.$gte && 
                 t.createdAt <= query.createdAt.$lte;
        }
        if (query.type) {
          return t.type === query.type;
        }
        return true;
      })
      .sort((a, b) => b.createdAt - a.createdAt)
      .slice((page - 1) * limit, page * limit);

    const total = earnings.transactions.length;

    res.status(200).json({
      success: true,
      data: {
        transactions,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error fetching transaction history',
      error: error.message
    });
  }
};

// Request a withdrawal
exports.requestWithdrawal = async (req, res) => {
  try {
    const { amount, bankDetails } = req.body;
    const barber = await Barber.findById(req.user._id);
    
    // Validate required fields
    if (!amount || !bankDetails) {
      return res.status(400).json({
        success: false,
        message: 'Amount and bank details are required'
      });
    }

    const { bankCode, bankName, accountNumber } = bankDetails;
    
    if (!bankCode || !bankName || !accountNumber) {
      return res.status(400).json({
        success: false,
        message: 'Bank code, bank name, and account number are required'
      });
    }

    // Validate withdrawal amount
    if (amount < 5000) {
      return res.status(400).json({
        success: false,
        message: 'Minimum withdrawal amount is ₦5,000'
      });
    }

    // Get or create earnings record
    const earnings = await BarberEarnings.getOrCreate(req.user._id);

    // Calculate pending withdrawals to get actual available balance
    const pendingWithdrawals = earnings.transactions
      .filter(t => t.type === 'WITHDRAWAL' && t.description.includes('(PENDING)'))
      .reduce((total, t) => total + Math.abs(t.amount), 0);
    
    const actualAvailableBalance = earnings.availableBalance - pendingWithdrawals;

    // Check against actual available balance (after pending withdrawals)
    if (amount > actualAvailableBalance) {
      return res.status(400).json({
        success: false,
        message: `Insufficient available balance. You have ₦${pendingWithdrawals.toLocaleString()} in pending withdrawals. Available for withdrawal: ₦${Math.max(0, actualAvailableBalance).toLocaleString()}`
      });
    }

    // Verify bank account with Monnify
    const accountValidation = await paymentService.verifyBankAccount(bankCode, accountNumber);
    if (!accountValidation.success) {
      return res.status(400).json({
        success: false,
        message: 'Invalid bank account details',
        error: accountValidation.error
      });
    }

    // Account verification is sufficient - no need to match names
    // This allows barbers to withdraw to any valid bank account

    // Generate withdrawal reference manually
    const withdrawalReference = `WTH-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    // Create withdrawal request
    const withdrawal = new Withdrawal({
      barber: req.user._id,
      amount,
      bankDetails: {
        bankCode,
        bankName,
        accountNumber,
        accountName: accountValidation.accountName
      },
      withdrawalReference
    });

    // Validate withdrawal limits
    await withdrawal.validateWithdrawalLimits();

    // No account age restrictions - barbers can withdraw immediately

    // Save withdrawal request (status: PENDING - do not deduct balance yet)
    await withdrawal.save();
    
    // Add PENDING withdrawal transaction to show in history
    earnings.transactions.push({
      type: 'WITHDRAWAL',
      amount: -withdrawal.amount,
      reference: withdrawal.withdrawalReference,
      description: `Withdrawal request to ${withdrawal.bankDetails.bankName} (PENDING)`,
      relatedWithdrawal: withdrawal._id,
      balanceAfter: {
        available: earnings.availableBalance, // Balance unchanged until admin approval
        pending: earnings.pendingBalance
      }
    });
    await earnings.save();

    // Send immediate confirmation email to barber
    await sendEmail({
      to: barber.email,
      subject: 'Withdrawal Request Received',
      template: 'withdrawal-request-received',
      data: {
        barberName: barber.fullName,
        amount: withdrawal.amount,
        bankDetails: withdrawal.bankDetails,
        withdrawalReference: withdrawal.withdrawalReference,
        submittedAt: new Date()
      }
    });

    // Send notification email to admin
    const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
    await sendEmail({
      to: adminEmail,
      subject: 'New Barber Withdrawal Request',
      template: 'admin-withdrawal-notification',
      data: {
        barberName: barber.fullName,
        barberEmail: barber.email,
        amount: withdrawal.amount,
        bankDetails: withdrawal.bankDetails,
        withdrawalReference: withdrawal.withdrawalReference,
        submittedAt: new Date(),
        adminDashboardUrl: `${process.env.CLIENT_URL}/admin/withdrawals`
      }
    });

    res.status(201).json({
      success: true,
      message: 'Withdrawal request submitted successfully',
      data: {
        withdrawal: {
          id: withdrawal._id,
          amount: withdrawal.amount,
          status: withdrawal.status,
          withdrawalReference: withdrawal.withdrawalReference,
          bankDetails: withdrawal.bankDetails,
          createdAt: withdrawal.createdAt
        },
        currentBalance: earnings.availableBalance // Balance unchanged until admin approval
      }
    });
  } catch (error) {
    console.error('Withdrawal request error:', error);
    res.status(error.message.includes('limit exceeded') ? 400 : 500).json({
      success: false,
      message: 'Error processing withdrawal request',
      error: error.message
    });
  }
};

// Get withdrawal history
exports.getWithdrawalHistory = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const status = req.query.status;

    let query = { barber: req.user._id };
    if (status) {
      query.status = status;
    }

    const withdrawals = await Withdrawal.find(query)
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(limit);

    const total = await Withdrawal.countDocuments(query);

    res.status(200).json({
      success: true,
      data: {
        withdrawals,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error fetching withdrawal history',
      error: error.message
    });
  }
};

// Admin: Process withdrawal request
exports.processWithdrawal = async (req, res) => {
  try {
    const { withdrawalId } = req.params;
    const { action, notes } = req.body;

    const withdrawal = await Withdrawal.findById(withdrawalId).populate('barber');
    if (!withdrawal) {
      return res.status(404).json({
        success: false,
        message: 'Withdrawal request not found'
      });
    }

    if (withdrawal.status !== 'PENDING') {
      return res.status(400).json({
        success: false,
        message: 'Withdrawal request has already been processed'
      });
    }

    if (!['approve', 'reject'].includes(action)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid action. Must be either approve or reject'
      });
    }

    const barber = withdrawal.barber;

    if (action === 'approve') {
      // Mark withdrawal as completed
      withdrawal.status = 'COMPLETED';
      withdrawal.metadata = {
        ...withdrawal.metadata,
        completedAt: new Date()
      };

      // Update barber earnings - NOW deduct from available balance
      const earnings = await BarberEarnings.findOne({ barber: withdrawal.barber._id });
      if (earnings) {
        // Check if barber still has sufficient balance
        if (withdrawal.amount > earnings.availableBalance) {
          return res.status(400).json({
            success: false,
            message: 'Insufficient balance to complete withdrawal'
          });
        }

        // Deduct from available balance
        earnings.availableBalance -= withdrawal.amount;
        earnings.totalWithdrawn += withdrawal.amount;
        earnings.lastWithdrawalDate = new Date();

        // Update the existing PENDING transaction to COMPLETED
        const pendingTransaction = earnings.transactions.find(
          t => t.relatedWithdrawal && 
               t.relatedWithdrawal.toString() === withdrawal._id.toString() && 
               t.type === 'WITHDRAWAL'
        );
        
        if (pendingTransaction) {
          pendingTransaction.description = `Withdrawal to ${withdrawal.bankDetails.bankName} (COMPLETED)`;
          pendingTransaction.balanceAfter = {
            available: earnings.availableBalance,
            pending: earnings.pendingBalance
          };
        } else {
          // Fallback: add new transaction if not found
          earnings.transactions.push({
            type: 'WITHDRAWAL',
            amount: -withdrawal.amount,
            reference: withdrawal.withdrawalReference,
            description: `Withdrawal to ${withdrawal.bankDetails.bankName} (COMPLETED)`,
            relatedWithdrawal: withdrawal._id,
            balanceAfter: {
              available: earnings.availableBalance,
              pending: earnings.pendingBalance
            }
          });
        }
        
        await earnings.save();
      }

      // Send completion email to barber
      await sendEmail({
        to: barber.email,
        subject: '💸 Withdrawal Completed',
        template: 'withdrawal-completed',
        data: {
          barberName: barber.fullName,
          amount: withdrawal.amount,
          bankDetails: withdrawal.bankDetails,
          withdrawalReference: withdrawal.withdrawalReference,
          completedAt: new Date(),
          updatedBalance: earnings ? earnings.availableBalance : 0
        }
      });

    } else if (action === 'reject') {
      // Mark withdrawal as failed
      withdrawal.status = 'FAILED';
      withdrawal.failureReason = notes;

      // Update the existing PENDING transaction to REJECTED (no balance change needed since it was never deducted)
      const earnings = await BarberEarnings.findOne({ barber: withdrawal.barber._id });
      if (earnings) {
        // Find and update the existing PENDING transaction
        const pendingTransaction = earnings.transactions.find(
          t => t.relatedWithdrawal && 
               t.relatedWithdrawal.toString() === withdrawal._id.toString() && 
               t.type === 'WITHDRAWAL'
        );
        
        if (pendingTransaction) {
          pendingTransaction.description = `Withdrawal request to ${withdrawal.bankDetails.bankName} (REJECTED: ${notes})`;
          pendingTransaction.balanceAfter = {
            available: earnings.availableBalance, // Balance unchanged
            pending: earnings.pendingBalance
          };
        }
        
        await earnings.save();
      }

      // Send rejection email to barber
      await sendEmail({
        to: barber.email,
        subject: 'Withdrawal Request Rejected',
        template: 'withdrawal-rejected',
        data: {
          barberName: barber.fullName,
          amount: withdrawal.amount,
          reason: notes,
          withdrawalReference: withdrawal.withdrawalReference,
          updatedBalance: earnings ? earnings.availableBalance : 0
        }
      });
    }

    withdrawal.processedBy = req.user._id;
    withdrawal.processedAt = new Date();
    withdrawal.notes = notes;
    await withdrawal.save();

    res.status(200).json({
      success: true,
      message: `Withdrawal request ${action === 'approve' ? 'approved' : 'rejected'} successfully`,
      data: withdrawal
    });
  } catch (error) {
    console.error('Process withdrawal error:', error);
    res.status(500).json({
      success: false,
      message: 'Error processing withdrawal request',
      error: error.message
    });
  }
};

// Admin: Get pending withdrawals
exports.getPendingWithdrawals = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const status = req.query.status || 'PENDING';

    const withdrawals = await Withdrawal.find({ status })
      .populate('barber', 'fullName email phone businessName')
      .sort({ createdAt: 1 }) // Oldest first to respect FIFO
      .skip((page - 1) * limit)
      .limit(limit);

    const total = await Withdrawal.countDocuments({ status });

    // Add SLA status to each withdrawal
    const withdrawalsWithSLA = withdrawals.map(withdrawal => {
      const createdAt = new Date(withdrawal.createdAt);
      const now = new Date();
      const hoursSinceCreation = (now - createdAt) / (1000 * 60 * 60);
      
      let slaStatus;
      if (hoursSinceCreation <= 24) {
        slaStatus = 'within_sla';
      } else if (hoursSinceCreation <= 48) {
        slaStatus = 'warning';
      } else {
        slaStatus = 'breached';
      }

      return {
        ...withdrawal.toObject(),
        slaStatus,
        hoursRemaining: Math.max(0, 48 - hoursSinceCreation)
      };
    });

    res.status(200).json({
      success: true,
      data: {
        withdrawals: withdrawalsWithSLA,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error fetching pending withdrawals',
      error: error.message
    });
  }
}; 