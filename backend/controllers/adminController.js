const Admin = require('../models/Admin');
const PasswordReset = require('../models/PasswordReset');
const { generateToken, authRateLimit, invalidateUserTokens } = require('../middleware/auth');
const { validateOTPAttempts } = require('../middleware/validation');
const emailService = require('../utils/emailService');
const VerificationRequest = require('../models/VerificationRequest');
const Barber = require('../models/Barber');
const User = require('../models/User');
const { sendEmail } = require('../utils/emailService');
const { catchAsync, AppError } = require('../utils/errorHandlers');
const Booking = require('../models/Booking');
const Payment = require('../models/Payment');
const Dispute = require('../models/Dispute');
const { cloudinary, generateSecureUrl } = require('../config/cloudinary');
const Subscription = require('../models/Subscription');
const Withdrawal = require('../models/Withdrawal');

// Admin Login
const adminLogin = async (req, res) => {
  try {
    const { email, password, role } = req.body;
    const ipAddress = req.ip || req.connection.remoteAddress;
    const userAgent = req.get('User-Agent') || 'Unknown';

    // Find admin by email
    const admin = await Admin.findByEmail(email);

    if (!admin) {
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials',
        code: 'INVALID_CREDENTIALS'
      });
    }

    // Check if account is suspended or inactive
    if (admin.status !== 'active') {
      return res.status(401).json({
        success: false,
        message: `Account is ${admin.status}. Please contact system administrator.`,
        code: 'ACCOUNT_INACTIVE'
      });
    }

    // Check if account is locked
    if (admin.isAccountLocked()) {
      const lockTimeRemaining = Math.ceil((admin.security.accountLockedUntil - new Date()) / (1000 * 60));
      return res.status(423).json({
        success: false,
        message: `Account is locked due to multiple failed login attempts. Try again in ${lockTimeRemaining} minutes.`,
        code: 'ACCOUNT_LOCKED',
        lockTimeRemaining: lockTimeRemaining
      });
    }

    // Verify password
    const isPasswordValid = await admin.comparePassword(password);

    if (!isPasswordValid) {
      // Increment failed login attempts
      admin.incrementFailedLoginAttempts();
      await admin.save();

      return res.status(401).json({
        success: false,
        message: 'Invalid credentials',
        code: 'INVALID_CREDENTIALS',
        attemptsRemaining: Math.max(0, 5 - admin.security.failedLoginAttempts)
      });
    }

    // Check if role matches
    if (role && admin.role !== role) {
      return res.status(401).json({
        success: false,
        message: 'Invalid role for this account',
        code: 'INVALID_ROLE'
      });
    }

    // Reset failed login attempts on successful login
    admin.resetFailedLoginAttempts();

    // Add login history entry
    const isNewDevice = admin.addLoginHistory(ipAddress, userAgent);

    // Update last login
    admin.lastLogin = new Date();
    admin.lastActivity = new Date();

    await admin.save();

    // Generate JWT token
    const token = generateToken(admin._id, admin.role);

    // Send security alert for new device
    if (isNewDevice) {
      try {
        await emailService.sendAdminSecurityAlertEmail(admin.email, admin.fullName, {
          ipAddress,
          userAgent,
          loginTime: new Date(),
          location: 'Unknown' // Could be enhanced with IP geolocation
        });
      } catch (emailError) {
        console.error('Failed to send security alert email:', emailError);
        // Don't fail login if email fails
      }
    }

    // Return success response
    res.status(200).json({
      success: true,
      message: 'Login successful',
      data: {
        admin: {
          id: admin._id,
          fullName: admin.fullName,
          email: admin.email,
          role: admin.role,
          permissions: admin.permissions,
          lastLogin: admin.lastLogin
        },
        token,
        isNewDevice
      }
    });

  } catch (error) {
    console.error('Admin login error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error during login'
    });
  }
};

// Get Admin Profile
const getAdminProfile = async (req, res) => {
  try {
    const admin = req.user; // Set by authenticateToken middleware

    res.status(200).json({
      success: true,
      data: {
        admin: admin.fullProfile
      }
    });

  } catch (error) {
    console.error('Get admin profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching profile'
    });
  }
};

// Update Admin Profile
const updateAdminProfile = async (req, res) => {
  try {
    const admin = req.user; // Set by authenticateToken middleware
    const { fullName, email, phoneNumber } = req.body;

    // Validate required fields
    if (!fullName || !email) {
      return res.status(400).json({
        success: false,
        message: 'Full name and email are required'
      });
    }

    // Check if email is already taken by another admin
    if (email !== admin.email) {
      const existingAdmin = await Admin.findOne({ email, _id: { $ne: admin._id } });
      if (existingAdmin) {
        return res.status(400).json({
          success: false,
          message: 'Email is already in use by another admin'
        });
      }
    }

    // Update profile fields
    admin.fullName = fullName.trim();
    admin.email = email.toLowerCase().trim();
    if (phoneNumber) {
      admin.phoneNumber = phoneNumber.trim();
    }

    // Update last modified timestamp
    admin.updatedAt = new Date();

    await admin.save();

    res.status(200).json({
      success: true,
      message: 'Profile updated successfully',
      data: {
        admin: admin.fullProfile
      }
    });

  } catch (error) {
    console.error('Update admin profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while updating profile'
    });
  }
};

// Admin Logout
const adminLogout = async (req, res) => {
  try {
    const admin = req.user;
    const ipAddress = req.ip || req.connection.remoteAddress;
    const userAgent = req.get('User-Agent') || 'Unknown';

    // Update last activity
    admin.lastActivity = new Date();
    await admin.save();

    // In a production environment, you might want to:
    // 1. Add the token to a blacklist
    // 2. Store session information in Redis
    // 3. Track logout events

    res.status(200).json({
      success: true,
      message: 'Logout successful'
    });

  } catch (error) {
    console.error('Admin logout error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error during logout'
    });
  }
};

// Request Password Reset
const requestAdminPasswordReset = async (req, res) => {
  try {
    const { email } = req.body;
    const ipAddress = req.ip || req.connection.remoteAddress;
    const userAgent = req.get('User-Agent') || 'Unknown';

    // Find admin by email
    const admin = await Admin.findByEmail(email);

    // Always return success message for security (prevent email enumeration)
    const successResponse = {
      success: true,
      message: 'If an admin account with this email exists, you will receive a password reset link shortly.'
    };

    if (!admin) {
      return res.status(200).json(successResponse);
    }

    // Check if admin account is active
    if (admin.status !== 'active') {
      return res.status(200).json(successResponse);
    }

    // Generate reset token
    const resetToken = PasswordReset.generateResetToken(admin.email, admin._id, 'admin', ipAddress, userAgent);
    await resetToken.save();

    // Send password reset email
    try {
      await emailService.sendAdminPasswordResetEmail(admin.email, admin.fullName, resetToken.token);
    } catch (emailError) {
      console.error('Failed to send password reset email:', emailError);
      // Don't reveal email sending failure to prevent enumeration
    }

    res.status(200).json(successResponse);

  } catch (error) {
    console.error('Admin password reset request error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error during password reset request'
    });
  }
};

// Reset Password
const resetAdminPassword = async (req, res) => {
  try {
    const { token, newPassword, confirmPassword } = req.body;

    // Verify passwords match
    if (newPassword !== confirmPassword) {
      return res.status(400).json({
        success: false,
        message: 'Passwords do not match',
        code: 'PASSWORDS_MISMATCH'
      });
    }

    // Find and validate reset token
    const resetToken = await PasswordReset.findValidToken(token);

    if (!resetToken) {
      return res.status(400).json({
        success: false,
        message: 'Invalid or expired reset token',
        code: 'INVALID_TOKEN'
      });
    }

    // Ensure this is an admin reset token
    if (resetToken.userType !== 'admin') {
      return res.status(400).json({
        success: false,
        message: 'Invalid reset token',
        code: 'INVALID_TOKEN'
      });
    }

    // Find admin
    const admin = await Admin.findById(resetToken.userId);

    if (!admin) {
      return res.status(400).json({
        success: false,
        message: 'Admin not found',
        code: 'ADMIN_NOT_FOUND'
      });
    }

    // Verify email matches (additional security check)
    if (admin.email !== resetToken.email) {
      return res.status(400).json({
        success: false,
        message: 'Invalid reset token',
        code: 'INVALID_TOKEN'
      });
    }

    // Update password (will be hashed by pre-save middleware)
    admin.password = newPassword;
    admin.security.passwordChangedAt = new Date();
    admin.security.lastPasswordChange = new Date();

    // Reset failed login attempts
    admin.resetFailedLoginAttempts();

    await admin.save();

    // Mark reset token as used
    await resetToken.markAsUsed();

    // Invalidate all other reset tokens for this admin
    await PasswordReset.invalidateUserTokens(admin._id, 'admin');

    // Invalidate all JWT tokens for this admin (force re-login on all devices)
    await invalidateUserTokens(admin._id, 'admin');

    // Send confirmation email
    try {
      await emailService.sendAdminPasswordResetConfirmationEmail(admin.email, admin.fullName);
    } catch (emailError) {
      console.error('Failed to send password reset confirmation email:', emailError);
      // Don't fail the reset if email fails
    }

    res.status(200).json({
      success: true,
      message: 'Password reset successful. Please login with your new password.'
    });

  } catch (error) {
    console.error('Admin password reset completion error:', error);

    // Handle validation errors
    if (error.name === 'ValidationError') {
      const errors = Object.values(error.errors).map(err => ({
        field: err.path,
        message: err.message
      }));

      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors
      });
    }

    res.status(500).json({
      success: false,
      message: 'Server error during password reset'
    });
  }
};

// Change Admin Password
const changeAdminPassword = async (req, res) => {
  try {
    const admin = req.user; // Set by authenticateToken middleware
    const { newPassword } = req.body;

    // Validate required fields
    if (!newPassword) {
      return res.status(400).json({
        success: false,
        message: 'New password is required'
      });
    }

    // Update password
    admin.password = newPassword;
    admin.updatedAt = new Date();
    await admin.save();

    // Generate new token
    const newToken = generateToken(admin._id, admin.role);

    res.status(200).json({
      success: true,
      message: 'Password updated successfully',
      data: {
        token: newToken
      }
    });

  } catch (error) {
    console.error('Change admin password error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while changing password'
    });
  }
};

// Helper function to generate signed URLs for documents
const generateSignedUrls = (documents) => {
  if (!documents) return null;

  const signedDocuments = {};
  for (const [key, doc] of Object.entries(documents)) {
    if (doc && doc.publicId) {
      const timestamp = Math.round(new Date().getTime() / 1000) + 3600; // 1 hour expiry
      const signature = cloudinary.utils.api_sign_request(
        {
          timestamp,
          public_id: doc.publicId,
          resource_type: doc.resourceType || 'image'
        },
        process.env.CLOUDINARY_API_SECRET
      );

      signedDocuments[key] = {
        ...doc,
        url: cloudinary.url(doc.publicId, {
          resource_type: doc.resourceType || 'image',
          sign_url: true,
          secure: true,
          type: 'upload',
          timestamp,
          signature
        })
      };
    }
  }
  return signedDocuments;
};

// Get Verification Requests
const getVerificationRequests = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50;
    const status = req.query.status || 'pending';
    const skip = (page - 1) * limit;

    // Build query to get barbers with pending verification
    const query = {
      registrationStatus: 'pending_verification'
    };

    // Get total count
    const total = await Barber.countDocuments(query);

    // Log the query and total for debugging
    console.log('Pending verification barbers query:', query);
    console.log('Total pending barbers:', total);

    // Fetch barbers with details
    const barbers = await Barber.find(query)
      .select('fullName email businessName phoneNumber address documents registrationStatus')
      .skip(skip)
      .limit(limit)
      .sort({ createdAt: -1 })
      .lean();

    // Transform barber data for frontend
    const transformedRequests = barbers.map(barber => ({
      _id: barber._id,
      status: 'pending',
      barberId: {
        _id: barber._id,
        fullName: barber.fullName || '',
        email: barber.email || '',
        businessName: barber.businessName || '',
        phoneNumber: barber.phoneNumber || '',
        address: barber.address || '',
        documents: barber.documents || {}
      }
    }));

    // Generate signed URLs for documents
    const requestsWithSignedUrls = transformedRequests.map(request => {
      if (request.barberId.documents) {
        request.barberId.documents = generateSignedUrls(request.barberId.documents);
      }
      return request;
    });

    const totalPages = Math.ceil(total / limit);

    // Return response
    res.status(200).json({
      success: true,
      data: {
        requests: requestsWithSignedUrls,
        pagination: {
          totalPages,
          currentPage: page,
          totalRequests: total
        }
      }
    });

  } catch (error) {
    console.error('Error fetching verification requests:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching verification requests',
      error: error.message
    });
  }
};

// Get Verification Request Details
const getVerificationRequestDetails = async (req, res) => {
  try {
    const { requestId } = req.params;

    const request = await VerificationRequest.findById(requestId)
      .populate('barberId', 'fullName email phoneNumber businessName documents')
      .populate('assignedTo', 'fullName email')
      .populate('auditTrail.performedBy', 'fullName email');

    if (!request) {
      return res.status(404).json({
        success: false,
        message: 'Verification request not found'
      });
    }

    res.status(200).json({
      success: true,
      data: { request }
    });

  } catch (error) {
    console.error('Get verification request details error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching verification request details'
    });
  }
};

// Assign Verification Request
const assignVerificationRequest = async (req, res) => {
  try {
    const { requestId } = req.params;
    const adminId = req.user._id;

    const request = await VerificationRequest.findById(requestId);

    if (!request) {
      return res.status(404).json({
        success: false,
        message: 'Verification request not found'
      });
    }

    if (request.status !== 'pending') {
      return res.status(400).json({
        success: false,
        message: 'Request cannot be assigned - invalid status'
      });
    }

    // Update request
    request.assignedTo = adminId;
    request.assignedAt = new Date();
    request.status = 'in_review';

    // Add audit entry
    request.addAuditEntry(
      'request_assigned',
      adminId,
      { assignedTo: adminId },
      req.ip,
      req.get('User-Agent')
    );

    await request.save();

    res.status(200).json({
      success: true,
      message: 'Verification request assigned successfully',
      data: { request }
    });

  } catch (error) {
    console.error('Assign verification request error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while assigning verification request'
    });
  }
};

// Start Verification Review
const startVerificationReview = async (req, res) => {
  try {
    const { requestId } = req.params;
    const adminId = req.user._id;

    const request = await VerificationRequest.findById(requestId);

    if (!request) {
      return res.status(404).json({
        success: false,
        message: 'Verification request not found'
      });
    }

    if (request.assignedTo.toString() !== adminId.toString()) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to review this request'
      });
    }

    if (request.status !== 'in_review') {
      return res.status(400).json({
        success: false,
        message: 'Invalid request status for starting review'
      });
    }

    request.reviewStartedAt = new Date();
    request.addAuditEntry(
      'review_started',
      adminId,
      {},
      req.ip,
      req.get('User-Agent')
    );

    await request.save();

    res.status(200).json({
      success: true,
      message: 'Review started successfully',
      data: { request }
    });

  } catch (error) {
    console.error('Start verification review error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while starting verification review'
    });
  }
};

// Verify Document
const verifyDocument = async (req, res) => {
  try {
    const { requestId } = req.params;
    const { documentType, verified, notes } = req.body;
    const adminId = req.user._id;

    const request = await VerificationRequest.findById(requestId);

    if (!request) {
      return res.status(404).json({
        success: false,
        message: 'Verification request not found'
      });
    }

    if (request.assignedTo.toString() !== adminId.toString()) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to verify documents for this request'
      });
    }

    if (!['cacCertificate', 'ninDocument', 'passportPhoto'].includes(documentType)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid document type'
      });
    }

    // Update document verification status
    request.documentsVerified[documentType] = {
      verified,
      notes,
      verifiedAt: new Date(),
      verifiedBy: adminId
    };

    request.addAuditEntry(
      'document_verified',
      adminId,
      { documentType, verified, notes },
      req.ip,
      req.get('User-Agent')
    );

    await request.save();

    res.status(200).json({
      success: true,
      message: 'Document verification status updated',
      data: { request }
    });

  } catch (error) {
    console.error('Verify document error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while verifying document'
    });
  }
};

// Complete Verification Review
const completeVerificationReview = async (req, res) => {
  try {
    const { requestId } = req.params;
    const { decision, rejectionReason } = req.body;
    const adminId = req.user._id;

    const request = await VerificationRequest.findById(requestId);

    if (!request) {
      return res.status(404).json({
        success: false,
        message: 'Verification request not found'
      });
    }

    if (request.assignedTo.toString() !== adminId.toString()) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to complete this review'
      });
    }

    if (!['approved', 'rejected'].includes(decision)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid decision'
      });
    }

    if (decision === 'rejected' && !rejectionReason) {
      return res.status(400).json({
        success: false,
        message: 'Rejection reason is required'
      });
    }

    // Update verification request
    request.status = decision === 'approved' ? 'approved' : 'rejected';
    request.decision = decision;
    request.rejectionReason = rejectionReason;
    request.reviewCompletedAt = new Date();

    // Update barber status
    const barber = await Barber.findById(request.barberId);
    if (!barber) {
      return res.status(404).json({
        success: false,
        message: 'Barber not found'
      });
    }

    barber.registrationStatus = decision === 'approved' ? 'verified' : 'rejected';
    if (decision === 'rejected') {
      barber.verification.rejectionReason = rejectionReason;
    }
    barber.verification.reviewedAt = new Date();
    barber.verification.reviewedBy = adminId;

    // Add audit entry
    request.addAuditEntry(
      'review_completed',
      adminId,
      { decision, rejectionReason },
      req.ip,
      req.get('User-Agent')
    );

    // Send appropriate notification
    const emailTemplate = decision === 'approved' 
      ? 'verificationApproved'
      : 'verificationRejected';
    
    await emailService.sendBarberVerificationEmail(
      barber.email,
      barber.fullName,
      {
        decision,
        rejectionReason,
        nextSteps: decision === 'approved' 
          ? 'You can now log in and start accepting bookings.'
          : 'Please address the issues mentioned and submit a new verification request.'
      }
    );

    // Save changes
    await Promise.all([request.save(), barber.save()]);

    res.status(200).json({
      success: true,
      message: `Verification request ${decision}`,
      data: { request }
    });

  } catch (error) {
    console.error('Complete verification review error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while completing verification review'
    });
  }
};

// Request Clarification
const requestClarification = async (req, res) => {
  try {
    const { requestId } = req.params;
    const { clarificationMessage } = req.body;
    const adminId = req.user._id;

    const request = await VerificationRequest.findById(requestId);

    if (!request) {
      return res.status(404).json({
        success: false,
        message: 'Verification request not found'
      });
    }

    if (request.assignedTo.toString() !== adminId.toString()) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to request clarification for this request'
      });
    }

    // Update request status
    request.status = 'pending';
    request.decision = 'needs_clarification';
    request.clarificationRequested = clarificationMessage;

    // Add audit entry
    request.addAuditEntry(
      'clarification_requested',
      adminId,
      { message: clarificationMessage },
      req.ip,
      req.get('User-Agent')
    );

    // Send notification to barber
    const barber = await Barber.findById(request.barberId);
    if (barber) {
      await emailService.sendBarberClarificationEmail(
        barber.email,
        barber.fullName,
        {
          message: clarificationMessage,
          requestId: request._id
        }
      );
    }

    await request.save();

    res.status(200).json({
      success: true,
      message: 'Clarification requested successfully',
      data: { request }
    });

  } catch (error) {
    console.error('Request clarification error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while requesting clarification'
    });
  }
};

// User Management Functions

// Get comprehensive user statistics
exports.getUserOverviewStats = catchAsync(async (req, res, next) => {
  // Get date ranges for comparison
  const today = new Date();
  const thisMonth = new Date(today.getFullYear(), today.getMonth(), 1);
  const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
  const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, 1);

  // Get comprehensive user stats
  const userStats = await User.aggregate([
    {
      $facet: {
        totalStats: [
          {
            $group: {
              _id: null,
              totalUsers: { $sum: 1 },
              verifiedUsers: {
                $sum: { $cond: [{ $eq: ["$status", "verified"] }, 1, 0] }
              },
              pendingUsers: {
                $sum: { $cond: [{ $eq: ["$status", "pending_verification"] }, 1, 0] }
              },
              suspendedUsers: {
                $sum: { $cond: [{ $eq: ["$status", "suspended"] }, 1, 0] }
              },
              activeUsers: {
                $sum: { $cond: [{ $ne: ["$status", "suspended"] }, 1, 0] }
              }
            }
          }
        ],
        monthlyStats: [
          {
            $group: {
              _id: {
                year: { $year: "$createdAt" },
                month: { $month: "$createdAt" }
              },
              count: { $sum: 1 },
              newUsersThisMonth: {
                $sum: {
                  $cond: [
                    {
                      $and: [
                        { $gte: ["$createdAt", thisMonth] },
                        { $lt: ["$createdAt", nextMonth] }
                      ]
                    },
                    1,
                    0
                  ]
                }
              },
              newUsersLastMonth: {
                $sum: {
                  $cond: [
                    {
                      $and: [
                        { $gte: ["$createdAt", lastMonth] },
                        { $lt: ["$createdAt", thisMonth] }
                      ]
                    },
                    1,
                    0
                  ]
                }
              }
            }
          },
          { $sort: { "_id.year": -1, "_id.month": -1 } },
          { $limit: 12 }
        ]
      }
    }
  ]);

  // Get booking stats for users
  const bookingStats = await Booking.aggregate([
    {
      $group: {
        _id: null,
        totalBookings: { $sum: 1 },
        averageBookingsPerUser: { $avg: 1 }
      }
    }
  ]);

  // Extract data
  const totalStats = userStats[0].totalStats[0] || {
    totalUsers: 0,
    verifiedUsers: 0,
    pendingUsers: 0,
    suspendedUsers: 0,
    activeUsers: 0
  };

  const monthlyData = userStats[0].monthlyStats || [];
  
  // Calculate this month and last month registrations
  const newUsersThisMonth = await User.countDocuments({
    createdAt: { $gte: thisMonth, $lt: nextMonth }
  });
  
  const newUsersLastMonth = await User.countDocuments({
    createdAt: { $gte: lastMonth, $lt: thisMonth }
  });

  // Calculate growth rate
  const userGrowthRate = newUsersLastMonth > 0 
    ? Math.round(((newUsersThisMonth - newUsersLastMonth) / newUsersLastMonth) * 100)
    : 0;

  // Calculate average bookings per user
  const totalBookings = bookingStats[0]?.totalBookings || 0;
  const averageBookingsPerUser = totalStats.totalUsers > 0 
    ? Number((totalBookings / totalStats.totalUsers).toFixed(1))
    : 0;

  res.status(200).json({
    success: true,
    data: {
      totalUsers: totalStats.totalUsers,
      verifiedUsers: totalStats.verifiedUsers,
      pendingUsers: totalStats.pendingUsers,
      suspendedUsers: totalStats.suspendedUsers,
      activeUsers: totalStats.activeUsers,
      newUsersThisMonth,
      newUsersLastMonth,
      userGrowthRate,
      averageBookingsPerUser,
      monthlyRegistrations: monthlyData
    }
  });
});

// Get paginated list of users with filters
exports.getUsers = catchAsync(async (req, res, next) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10;
  const skip = (page - 1) * limit;
  
  const filter = {};
  if (req.query.status) filter.status = req.query.status;
  if (req.query.role) filter.role = req.query.role;
  if (req.query.search) {
    filter.$or = [
      { fullName: new RegExp(req.query.search, 'i') },
      { email: new RegExp(req.query.search, 'i') }
    ];
  }

  const users = await User.find(filter)
    .select('-password -otp')
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit);

  const total = await User.countDocuments(filter);

  res.status(200).json({
    success: true,
    data: users,
    pagination: {
      total,
      page,
      pages: Math.ceil(total / limit)
    }
  });
});

// Get single user profile
exports.getUserProfile = catchAsync(async (req, res, next) => {
  const user = await User.findById(req.params.userId)
    .select('-password -otp');
  
  if (!user) {
    return next(new AppError('User not found', 404));
  }

  res.status(200).json({
    success: true,
    data: user
  });
});

// Update user status (suspend/reactivate)
exports.updateUserStatus = catchAsync(async (req, res, next) => {
  const { status, suspensionReason } = req.body;
  const validStatuses = ['verified', 'suspended', 'inactive'];
  
  console.log('\n👤 Updating user status:');
  console.log('- Status:', status);
  if (suspensionReason) console.log('- Reason:', suspensionReason);
  
  if (!validStatuses.includes(status)) {
    console.log('❌ Invalid status:', status);
    return next(new AppError('Invalid status provided', 400));
  }

  // If status is suspended, require a reason
  if (status === 'suspended' && !suspensionReason) {
    console.log('❌ Missing suspension reason');
    return next(new AppError('Suspension reason is required', 400));
  }

  const user = await User.findById(req.params.userId);
  if (!user) {
    console.log('❌ User not found:', req.params.userId);
    return next(new AppError('User not found', 404));
  }

  console.log('- User found:', user.email);
  console.log('- Previous status:', user.status);

  user.status = status;
  if (status === 'suspended') {
    user.suspensionReason = suspensionReason;
  } else if (status === 'verified') {
    user.suspensionReason = null; // Clear suspension reason when unsuspending
  }
  
  await user.save();
  console.log('✅ User status updated in database');

  // Send email notification to user
  try {
    await emailService.sendStatusUpdateEmail(user.email, user.fullName, {
      status,
      reason: suspensionReason
    });
  } catch (error) {
    console.error('⚠️ Email sending failed but continuing with response');
    console.error('- Error:', error.message);
  }

  res.status(200).json({
    success: true,
    message: 'User status updated successfully',
    data: { 
      status: user.status,
      suspensionReason: user.suspensionReason 
    }
  });
});

// Bulk email users
exports.sendBulkEmail = catchAsync(async (req, res, next) => {
  const { userIds, subject, message } = req.body;

  if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
    return next(new AppError('Please provide an array of user IDs', 400));
  }

  if (!subject || !message) {
    return next(new AppError('Subject and message are required', 400));
  }

  const users = await User.find({ _id: { $in: userIds } });
  const emailPromises = users.map(user => 
    sendEmail({
      to: user.email,
      subject,
      text: message
    })
  );

  await Promise.all(emailPromises);

  res.status(200).json({
    success: true,
    message: `Emails sent successfully to ${users.length} users`
  });
});

// Update user verification status
exports.updateVerificationStatus = catchAsync(async (req, res, next) => {
  const { userId } = req.params;
  const { verified } = req.body;

  const user = await User.findById(userId);
  if (!user) {
    return next(new AppError('User not found', 404));
  }

  user.status = verified ? 'verified' : 'pending_verification';
  user.emailVerifiedAt = verified ? new Date() : null;
  await user.save();

  // Send email notification
  await sendEmail({
    to: user.email,
    subject: 'Verification Status Update',
    text: `Your account verification status has been updated to: ${user.status}`
  });

  res.status(200).json({
    success: true,
    message: 'User verification status updated successfully',
    data: {
      status: user.status,
      emailVerifiedAt: user.emailVerifiedAt
    }
  });
});

// Get dashboard statistics
exports.getDashboardStats = catchAsync(async (req, res, next) => {
  // Get date ranges for comparison
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);
  
  const thisMonth = new Date(today.getFullYear(), today.getMonth(), 1);
  const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
  const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, 1);

  // Get user stats
  const userStats = await User.aggregate([
    {
      $facet: {
        totalUsers: [
          { $match: { role: 'user' } },
          { $count: 'count' }
        ],
        newUsersThisMonth: [
          { 
            $match: { 
              role: 'user',
              createdAt: { $gte: thisMonth, $lt: nextMonth } 
            } 
          },
          { $count: 'count' }
        ],
        newUsersLastMonth: [
          { 
            $match: { 
              role: 'user',
              createdAt: { $gte: lastMonth, $lt: thisMonth } 
            } 
          },
          { $count: 'count' }
        ]
      }
    }
  ]);

  // Get barber stats
  const barberStats = await User.aggregate([
    {
      $facet: {
        totalBarbers: [
          { $match: { role: 'barber' } },
          { $count: 'count' }
        ],
        newBarbersThisMonth: [
          { 
            $match: { 
              role: 'barber',
              createdAt: { $gte: thisMonth, $lt: nextMonth } 
            } 
          },
          { $count: 'count' }
        ],
        newBarbersLastMonth: [
          { 
            $match: { 
              role: 'barber',
              createdAt: { $gte: lastMonth, $lt: thisMonth } 
            } 
          },
          { $count: 'count' }
        ]
      }
    }
  ]);

  // Get booking stats
  const bookingStats = await Booking.aggregate([
    {
      $facet: {
        todayBookings: [
          {
            $match: {
              date: {
                $gte: new Date(today.setHours(0, 0, 0, 0)),
                $lt: new Date(today.setHours(23, 59, 59, 999))
              }
            }
          },
          { $count: 'count' }
        ],
        yesterdayBookings: [
          {
            $match: {
              date: {
                $gte: new Date(yesterday.setHours(0, 0, 0, 0)),
                $lt: new Date(yesterday.setHours(23, 59, 59, 999))
              }
            }
          },
          { $count: 'count' }
        ],
        thisMonthBookings: [
          {
            $match: {
              date: { $gte: thisMonth, $lt: nextMonth }
            }
          },
          { $count: 'count' }
        ],
        lastMonthBookings: [
          {
            $match: {
              date: { $gte: lastMonth, $lt: thisMonth }
            }
          },
          { $count: 'count' }
        ]
      }
    }
  ]);

  // Get revenue stats
  const revenueStats = await Payment.aggregate([
    {
      $facet: {
        todayRevenue: [
          {
            $match: {
              createdAt: {
                $gte: new Date(today.setHours(0, 0, 0, 0)),
                $lt: new Date(today.setHours(23, 59, 59, 999))
              },
              status: 'completed'
            }
          },
          {
            $group: {
              _id: null,
              total: { $sum: '$amount' }
            }
          }
        ],
        yesterdayRevenue: [
          {
            $match: {
              createdAt: {
                $gte: new Date(yesterday.setHours(0, 0, 0, 0)),
                $lt: new Date(yesterday.setHours(23, 59, 59, 999))
              },
              status: 'completed'
            }
          },
          {
            $group: {
              _id: null,
              total: { $sum: '$amount' }
            }
          }
        ],
        thisMonthRevenue: [
          {
            $match: {
              createdAt: { $gte: thisMonth, $lt: nextMonth },
              status: 'completed'
            }
          },
          {
            $group: {
              _id: null,
              total: { $sum: '$amount' },
              totalCommission: { $sum: '$platformCommission' },
              count: { $sum: 1 }
            }
          }
        ],
        lastMonthRevenue: [
          {
            $match: {
              createdAt: { $gte: lastMonth, $lt: thisMonth },
              status: 'completed'
            }
          },
          {
            $group: {
              _id: null,
              total: { $sum: '$amount' },
              totalCommission: { $sum: '$platformCommission' },
              count: { $sum: 1 }
            }
          }
        ]
      }
    }
  ]);

  // Extract values and calculate growth percentages
  const totalUsers = userStats[0].totalUsers[0]?.count || 0;
  const newUsersThisMonth = userStats[0].newUsersThisMonth[0]?.count || 0;
  const newUsersLastMonth = userStats[0].newUsersLastMonth[0]?.count || 0;
  const userGrowth = newUsersLastMonth > 0 
    ? Math.round(((newUsersThisMonth - newUsersLastMonth) / newUsersLastMonth) * 100) 
    : 0;

  const totalBarbers = barberStats[0].totalBarbers[0]?.count || 0;
  const newBarbersThisMonth = barberStats[0].newBarbersThisMonth[0]?.count || 0;
  const newBarbersLastMonth = barberStats[0].newBarbersLastMonth[0]?.count || 0;
  const barberGrowth = newBarbersLastMonth > 0 
    ? Math.round(((newBarbersThisMonth - newBarbersLastMonth) / newBarbersLastMonth) * 100) 
    : 0;

  const dailyBookings = bookingStats[0].todayBookings[0]?.count || 0;
  const yesterdayBookings = bookingStats[0].yesterdayBookings[0]?.count || 0;
  const bookingGrowth = yesterdayBookings > 0 
    ? Math.round(((dailyBookings - yesterdayBookings) / yesterdayBookings) * 100) 
    : 0;

  const thisMonthBookings = bookingStats[0].thisMonthBookings[0]?.count || 0;
  const lastMonthBookings = bookingStats[0].lastMonthBookings[0]?.count || 0;
  const monthlyBookingGrowth = lastMonthBookings > 0 
    ? Math.round(((thisMonthBookings - lastMonthBookings) / lastMonthBookings) * 100) 
    : 0;

  const todayRevenue = revenueStats[0].todayRevenue[0]?.total || 0;
  const yesterdayRevenue = revenueStats[0].yesterdayRevenue[0]?.total || 0;
  const revenueGrowth = yesterdayRevenue > 0 
    ? Math.round(((todayRevenue - yesterdayRevenue) / yesterdayRevenue) * 100) 
    : 0;

  const thisMonthRevenue = revenueStats[0].thisMonthRevenue[0]?.total || 0;
  const lastMonthRevenue = revenueStats[0].lastMonthRevenue[0]?.total || 0;
  const monthlyRevenueGrowth = lastMonthRevenue > 0 
    ? Math.round(((thisMonthRevenue - lastMonthRevenue) / lastMonthRevenue) * 100) 
    : 0;

  res.status(200).json({
    success: true,
    data: {
      totalUsers,
      totalBarbers,
      dailyBookings,
      revenue: Math.round(todayRevenue / 100), // Assuming amount is stored in cents
      userGrowth,
      barberGrowth,
      bookingGrowth,
      revenueGrowth,
      thisMonthBookings,
      thisMonthRevenue: Math.round(thisMonthRevenue / 100),
      monthlyBookingGrowth,
      monthlyRevenueGrowth
    }
  });
});

// Get recent activity
exports.getRecentActivity = catchAsync(async (req, res, next) => {
  const limit = parseInt(req.query.limit) || 10;
  
  // Get recent user registrations
  const recentUsers = await User.find()
    .sort({ createdAt: -1 })
    .limit(5)
    .select('fullName role createdAt');
  
  // Get recent bookings
  const recentBookings = await Booking.find()
    .sort({ createdAt: -1 })
    .limit(5)
    .populate('user', 'fullName')
    .populate('barber', 'fullName')
    .select('user barber status createdAt');
  
  // Get recent disputes
  const recentDisputes = await Dispute.find()
    .sort({ createdAt: -1 })
    .limit(5)
    .populate('user', 'fullName')
    .select('title status createdAt');
  
  // Combine and format activities
  const activities = [
    ...recentUsers.map(user => ({
      type: 'registration',
      message: `New ${user.role} registration: ${user.fullName}`,
      time: formatTimeAgo(user.createdAt),
      date: user.createdAt,
      id: user._id
    })),
    ...recentBookings.map(booking => ({
      type: 'booking',
      message: `New booking: ${booking.user.fullName} with ${booking.barber.fullName}`,
      time: formatTimeAgo(booking.createdAt),
      date: booking.createdAt,
      id: booking._id,
      status: booking.status
    })),
    ...recentDisputes.map(dispute => ({
      type: 'dispute',
      message: `New dispute: ${dispute.title}`,
      time: formatTimeAgo(dispute.createdAt),
      date: dispute.createdAt,
      id: dispute._id,
      status: dispute.status
    }))
  ];
  
  // Sort by date (newest first) and limit
  const sortedActivities = activities
    .sort((a, b) => b.date - a.date)
    .slice(0, limit);
  
  res.status(200).json({
    success: true,
    data: {
      recentActivity: sortedActivities
    }
  });
});

// Helper function to format time ago
function formatTimeAgo(date) {
  const now = new Date();
  const diffMs = now - date;
  const diffSec = Math.floor(diffMs / 1000);
  const diffMin = Math.floor(diffSec / 60);
  const diffHour = Math.floor(diffMin / 60);
  const diffDay = Math.floor(diffHour / 24);
  
  if (diffSec < 60) {
    return `${diffSec} seconds ago`;
  } else if (diffMin < 60) {
    return `${diffMin} minute${diffMin > 1 ? 's' : ''} ago`;
  } else if (diffHour < 24) {
    return `${diffHour} hour${diffHour > 1 ? 's' : ''} ago`;
  } else if (diffDay < 30) {
    return `${diffDay} day${diffDay > 1 ? 's' : ''} ago`;
  } else {
    return date.toLocaleDateString();
  }
}

// Get barber statistics
exports.getBarberStats = catchAsync(async (req, res) => {
  const barberStats = await Barber.aggregate([
    {
      $group: {
        _id: null,
        totalBarbers: { $sum: 1 },
        approvedBarbers: {
          $sum: {
            $cond: [
              { $eq: ["$registrationStatus", "verified"] },
              1,
              0
            ]
          }
        },
        rejectedApplications: {
          $sum: {
            $cond: [
              { $eq: ["$registrationStatus", "rejected"] },
              1,
              0
            ]
          }
        },
        pendingApplications: {
          $sum: {
            $cond: [
              { $eq: ["$registrationStatus", "pending_verification"] },
              1,
              0
            ]
          }
        }
      }
    }
  ]);

  // If no stats found, return default values
  const stats = barberStats[0] || {
    totalBarbers: 0,
    approvedBarbers: 0,
    rejectedApplications: 0,
    pendingApplications: 0
  };

  res.status(200).json({
    success: true,
    data: stats
  });
});

// Delete user
exports.deleteUser = catchAsync(async (req, res, next) => {
  const { userId } = req.params;

  const user = await User.findById(userId);
  if (!user) {
    return next(new AppError('User not found', 404));
  }

  // Delete the user
  await User.findByIdAndDelete(userId);

  // Send email notification to user
  try {
    await sendEmail({
      to: user.email,
      subject: 'Account Deletion Notification',
      text: `Dear ${user.fullName},\n\nYour account has been deleted by an administrator. If you believe this was done in error, please contact our support team.\n\nBest regards,\nThe Etch Team`
    });
  } catch (error) {
    console.error('Failed to send account deletion email:', error);
    // Continue with the response even if email fails
  }

  res.status(200).json({
    success: true,
    message: 'User deleted successfully'
  });
});

// Approve Verification Request
const approveVerificationRequest = catchAsync(async (req, res) => {
  const { barberId } = req.params;
  const { approvalMessage } = req.body;

  // Find the barber
  const barber = await Barber.findById(barberId);
  if (!barber) {
    throw new AppError('Barber not found', 404);
  }

  if (barber.registrationStatus !== 'pending_verification') {
    throw new AppError('Barber is not pending verification', 400);
  }

  // Update barber status
  barber.verificationStatus = 'verified';
  barber.isVerified = true;
  barber.registrationStatus = 'verified';
  barber.status = 'active';

  // Save the changes
  await barber.save();

  // Send approval email
  await emailService.sendVerificationApprovalEmail(barber.email, {
    barberName: barber.fullName,
    businessName: barber.businessName,
    approvalMessage
  });

  res.status(200).json({
    success: true,
    message: 'Barber approved successfully',
    data: { barber }
  });
});

// Reject Verification Request
const rejectVerificationRequest = catchAsync(async (req, res) => {
  const { barberId } = req.params;
  const { rejectionReason } = req.body;

  if (!rejectionReason || !rejectionReason.trim()) {
    throw new AppError('Rejection reason is required', 400);
  }

  // Find the barber
  const barber = await Barber.findById(barberId);
  if (!barber) {
    throw new AppError('Barber not found', 404);
  }

  if (barber.registrationStatus !== 'pending_verification') {
    throw new AppError('Barber is not pending verification', 400);
  }

  // Update barber status
  barber.verificationStatus = 'rejected';
  barber.registrationStatus = 'rejected';
  barber.status = 'inactive';
  barber.rejectionReason = rejectionReason;
  barber.rejectedAt = new Date();

  // Save the changes
  await barber.save();

  // Send rejection email
  await emailService.sendVerificationRejectionEmail(barber.email, {
    barberName: barber.fullName,
    businessName: barber.businessName,
    rejectionReason
  });

  res.status(200).json({
    success: true,
    message: 'Barber rejected successfully',
    data: { barber }
  });
});

// Get Verified Barbers (excludes banned barbers - they appear in banned section)
exports.getVerifiedBarbers = catchAsync(async (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10;
  const status = req.query.status || 'all';
  const skip = (page - 1) * limit;

  // Build base query - get all barbers approved by admin but exclude banned ones
  const baseQuery = {
    $or: [
      { registrationStatus: 'verified' },
      { verificationStatus: 'verified' }
    ],
    // Exclude banned barbers - they should only appear in banned section
    $nor: [
      { status: 'banned' }
    ]
  };

  // Add status filter if not 'all' (but still exclude banned)
  if (status !== 'all') {
    if (status === 'active') {
      baseQuery.$and = [
        {
          $or: [
            { status: 'active' },
            { status: { $exists: false } }
          ]
        }
      ];
    } else if (status === 'inactive') {
      baseQuery.status = 'inactive';
    }
  }

  // Get total count
  const total = await Barber.countDocuments(baseQuery);

  // Fetch barbers with pagination
  const barbers = await Barber.find(baseQuery)
    .select('fullName email businessName phoneNumber address status documents services profile.profilePicture isProfileActive createdAt verifiedAt registrationStatus')
    .skip(skip)
    .limit(limit)
    .sort({ createdAt: -1 })
    .lean();

  // Transform barber data for frontend
  const transformedBarbers = barbers.map(barber => {
    // Only include valid image URLs, filter out test images and invalid URLs
    let profileImage = null;
    const possibleImages = [
      barber.profile?.profilePicture,
      barber.documents?.passportPhoto?.url
    ];
    
    for (const img of possibleImages) {
      if (img && 
          typeof img === 'string' && 
          !img.includes('test_photo') && 
          !img.includes('undefined') &&
          (img.startsWith('http') || img.startsWith('https'))) {
        profileImage = img;
        break;
      }
    }

    return {
      _id: barber._id,
      name: barber.fullName,
      email: barber.email,
      phone: barber.phoneNumber,
      businessName: barber.businessName,
      address: barber.address,
      status: barber.status || 'active',
      services: barber.services || [],
      profileImage,
      isProfileActive: barber.isProfileActive || false,
      joinedDate: barber.createdAt
    };
  });

  const totalPages = Math.ceil(total / limit);

  res.status(200).json({
    success: true,
    data: {
      barbers: transformedBarbers,
      pagination: {
        totalPages,
        currentPage: page,
        totalBarbers: total
      }
    }
  });
});

// ============================================
// ADMIN BOOKING MANAGEMENT
// ============================================

// Get All Completed Bookings
exports.getAllCompletedBookings = catchAsync(async (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 20;
  const searchTerm = req.query.search || '';
  const skip = (page - 1) * limit;

  // Build query for completed bookings (no search here since we need to populate first)
  const query = {
    status: 'completed'
  };

  // Get total count (without search)
  let total = await Booking.countDocuments(query);

  // Fetch completed bookings with populated data - fix field selection to match model
  let bookingsQuery = Booking.find(query)
    .populate('user', 'fullName email phoneNumber profileImage')
    .populate('barber', 'fullName email businessName phoneNumber profileImage')
    .populate('service', 'name price duration category')
    .select('date startTime endTime totalPrice paymentMethod paymentStatus review serviceType address phoneNumber clientType numAdults numKids createdAt updatedAt')
    .sort({ updatedAt: -1 }) // Sort by last updated instead of completedAt which doesn't exist
    .lean();

  // If no search term, apply pagination now
  if (!searchTerm) {
    bookingsQuery = bookingsQuery.skip(skip).limit(limit);
  }

  let bookings = await bookingsQuery;

  // Apply search filter after population if search term is provided
  if (searchTerm) {
    bookings = bookings.filter(booking => {
      const customerName = booking.user?.fullName || '';
      const barberName = booking.barber?.fullName || '';
      const serviceName = booking.service?.name || '';
      
      return customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
             barberName.toLowerCase().includes(searchTerm.toLowerCase()) ||
             serviceName.toLowerCase().includes(searchTerm.toLowerCase());
    });
    
    // Update total count for search results
    total = bookings.length;
    
    // Apply pagination to search results
    bookings = bookings.slice(skip, skip + limit);
  }

  // Transform booking data for frontend display
  const transformedBookings = bookings.map(booking => {
    // Calculate service duration in a readable format
    const serviceDuration = booking.service?.duration ? `${booking.service.duration} mins` : 'N/A';
    
    // Format booking date and time
    const bookingDate = new Date(booking.date);
    const formattedDate = bookingDate.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
    
    const formattedTime = booking.startTime;
    
    // Calculate completion date - use updatedAt since completedAt doesn't exist in model
    const completedDate = booking.updatedAt ? new Date(booking.updatedAt).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }) : 'N/A';

    // Get customer info (person count)
    const customerInfo = [];
    if (booking.numAdults > 0) customerInfo.push(`${booking.numAdults} Adult${booking.numAdults > 1 ? 's' : ''}`);
    if (booking.numKids > 0) customerInfo.push(`${booking.numKids} Kid${booking.numKids > 1 ? 's' : ''}`);
    const customerCount = customerInfo.length > 0 ? customerInfo.join(', ') : '1 Adult';

    return {
      _id: booking._id,
      bookingId: booking._id.toString().slice(-8).toUpperCase(), // Short booking ID
      customer: {
        name: booking.user?.fullName || 'Unknown Customer',
        email: booking.user?.email || '',
        phone: booking.user?.phoneNumber || booking.phoneNumber || '',
        image: booking.user?.profileImage || null
      },
      barber: {
        name: booking.barber?.fullName || 'Unknown Barber',
        businessName: booking.barber?.businessName || '',
        email: booking.barber?.email || '',
        phone: booking.barber?.phoneNumber || '',
        image: booking.barber?.profileImage || null
      },
      service: {
        name: booking.service?.name || 'Unknown Service',
        category: booking.service?.category || '',
        duration: serviceDuration,
        price: booking.service?.price || booking.totalPrice || 0
      },
      booking: {
        date: formattedDate,
        time: formattedTime,
        serviceType: booking.serviceType || 'shop',
        address: booking.address || 'Shop location',
        clientType: booking.clientType || 'adult',
        customerCount,
        totalPrice: booking.totalPrice || 0,
        paymentMethod: booking.paymentMethod || 'monnify',
        paymentStatus: booking.paymentStatus || 'completed'
      },
      completion: {
        completedAt: completedDate,
        rating: booking.review?.rating || 0, // Fix: access nested rating
        review: booking.review?.comment || '', // Fix: access nested comment
        hasReview: !!(booking.review?.comment && booking.review.comment.trim()) // Fix: check nested comment
      },
      timeline: {
        booked: new Date(booking.createdAt).toLocaleDateString('en-US', {
          month: 'short',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        }),
        completed: booking.updatedAt ? new Date(booking.updatedAt).toLocaleDateString('en-US', {
          month: 'short',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        }) : 'N/A'
      }
    };
  });

  // Calculate summary statistics
  const totalRevenue = transformedBookings.reduce((sum, booking) => sum + (booking.booking.totalPrice || 0), 0);
  const averageRating = transformedBookings.length > 0 
    ? (transformedBookings.reduce((sum, booking) => sum + (booking.completion.rating || 0), 0) / transformedBookings.length).toFixed(1)
    : 0;
  const reviewsCount = transformedBookings.filter(booking => booking.completion.hasReview).length;

  const totalPages = Math.ceil(total / limit);

  res.status(200).json({
    success: true,
    data: {
      bookings: transformedBookings,
      summary: {
        totalCompletedBookings: total,
        totalRevenue: Math.round(totalRevenue / 100), // Convert from cents to main currency
        averageRating: parseFloat(averageRating),
        reviewsCount,
        currentPageBookings: transformedBookings.length
      },
      pagination: {
        totalPages,
        currentPage: page,
        totalBookings: total,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      }
    }
  });
});

// Get All Bookings (including pending, confirmed, completed, etc.)
exports.getAllBookings = catchAsync(async (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 20;
  const status = req.query.status || 'all';
  const searchTerm = req.query.search || '';
  const skip = (page - 1) * limit;

  // Build query (no search here since we need to populate first)
  let query = {};
  
  // Add status filter
  if (status !== 'all') {
    query.status = status;
  }

  // Get total count (without search)
  let total = await Booking.countDocuments(query);

  // Fetch bookings with populated data - fix field selection to match model
  let bookingsQuery = Booking.find(query)
    .populate('user', 'fullName email phoneNumber profileImage')
    .populate('barber', 'fullName email businessName phoneNumber profileImage')
    .populate('service', 'name price duration category')
    .select('date startTime endTime totalPrice paymentMethod paymentStatus status review serviceType address phoneNumber clientType numAdults numKids createdAt updatedAt')
    .sort({ createdAt: -1 })
    .lean();

  // If no search term, apply pagination now
  if (!searchTerm) {
    bookingsQuery = bookingsQuery.skip(skip).limit(limit);
  }

  let bookings = await bookingsQuery;

  // Apply search filter after population if search term is provided
  if (searchTerm) {
    bookings = bookings.filter(booking => {
      const customerName = booking.user?.fullName || '';
      const barberName = booking.barber?.fullName || '';
      const serviceName = booking.service?.name || '';
      
      return customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
             barberName.toLowerCase().includes(searchTerm.toLowerCase()) ||
             serviceName.toLowerCase().includes(searchTerm.toLowerCase());
    });
    
    // Update total count for search results
    total = bookings.length;
    
    // Apply pagination to search results
    bookings = bookings.slice(skip, skip + limit);
  }

  // Transform booking data (similar to completed bookings but with status info)
  const transformedBookings = bookings.map(booking => {
    const serviceDuration = booking.service?.duration ? `${booking.service.duration} mins` : 'N/A';
    
    const bookingDate = new Date(booking.date);
    const formattedDate = bookingDate.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
    
    const formattedTime = booking.startTime;

    const customerInfo = [];
    if (booking.numAdults > 0) customerInfo.push(`${booking.numAdults} Adult${booking.numAdults > 1 ? 's' : ''}`);
    if (booking.numKids > 0) customerInfo.push(`${booking.numKids} Kid${booking.numKids > 1 ? 's' : ''}`);
    const customerCount = customerInfo.length > 0 ? customerInfo.join(', ') : '1 Adult';

    return {
      _id: booking._id,
      bookingId: booking._id.toString().slice(-8).toUpperCase(),
      customer: {
        name: booking.user?.fullName || 'Unknown Customer',
        email: booking.user?.email || '',
        phone: booking.user?.phoneNumber || booking.phoneNumber || '',
        image: booking.user?.profileImage || null
      },
      barber: {
        name: booking.barber?.fullName || 'Unknown Barber',
        businessName: booking.barber?.businessName || '',
        email: booking.barber?.email || '',
        phone: booking.barber?.phoneNumber || '',
        image: booking.barber?.profileImage || null
      },
      service: {
        name: booking.service?.name || 'Unknown Service',
        category: booking.service?.category || '',
        duration: serviceDuration,
        price: booking.service?.price || booking.totalPrice || 0
      },
      booking: {
        date: formattedDate,
        time: formattedTime,
        serviceType: booking.serviceType || 'shop',
        address: booking.address || 'Shop location',
        clientType: booking.clientType || 'adult',
        customerCount,
        totalPrice: booking.totalPrice || 0,
        paymentMethod: booking.paymentMethod || 'monnify',
        paymentStatus: booking.paymentStatus || 'pending',
        status: booking.status || 'pending'
      },
      completion: {
        completedAt: booking.updatedAt && booking.status === 'completed' ? new Date(booking.updatedAt).toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        }) : null,
        rating: booking.review?.rating || 0, // Fix: access nested rating
        review: booking.review?.comment || '', // Fix: access nested comment
        hasReview: !!(booking.review?.comment && booking.review.comment.trim()) // Fix: check nested comment
      },
      timeline: {
        booked: new Date(booking.createdAt).toLocaleDateString('en-US', {
          month: 'short',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        }),
        completed: booking.updatedAt && booking.status === 'completed' ? new Date(booking.updatedAt).toLocaleDateString('en-US', {
          month: 'short',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        }) : null
      }
    };
  });

  const totalPages = Math.ceil(total / limit);

  res.status(200).json({
    success: true,
    data: {
      bookings: transformedBookings,
      pagination: {
        totalPages,
        currentPage: page,
        totalBookings: total,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      }
    }
  });
});

// Get Pending Bookings
exports.getPendingBookings = catchAsync(async (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 20;
  const skip = (page - 1) * limit;

  // Query for pending bookings
  const query = {
    status: { $in: ['pending', 'confirmed', 'in_progress'] }
  };

  const total = await Booking.countDocuments(query);

  const bookings = await Booking.find(query)
    .populate('user', 'fullName email phoneNumber profileImage')
    .populate('barber', 'fullName email businessName phoneNumber profileImage')
    .populate('service', 'name price duration category')
    .select('date startTime endTime totalPrice paymentMethod paymentStatus status serviceType address phoneNumber clientType numAdults numKids createdAt')
    .skip(skip)
    .limit(limit)
    .sort({ date: 1 }) // Sort by upcoming date
    .lean();

  // Transform data with urgency indicators
  const transformedBookings = bookings.map(booking => {
    const bookingDateTime = new Date(booking.date);
    const now = new Date();
    const timeDifference = bookingDateTime - now;
    const hoursUntil = Math.ceil(timeDifference / (1000 * 60 * 60));
    
    // Determine urgency level
    let urgency = 'low';
    if (hoursUntil <= 2) urgency = 'critical';
    else if (hoursUntil <= 24) urgency = 'high';
    else if (hoursUntil <= 72) urgency = 'medium';

    const formattedDate = bookingDateTime.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });

    const customerInfo = [];
    if (booking.numAdults > 0) customerInfo.push(`${booking.numAdults} Adult${booking.numAdults > 1 ? 's' : ''}`);
    if (booking.numKids > 0) customerInfo.push(`${booking.numKids} Kid${booking.numKids > 1 ? 's' : ''}`);
    const customerCount = customerInfo.length > 0 ? customerInfo.join(', ') : '1 Adult';

    return {
      _id: booking._id,
      bookingId: booking._id.toString().slice(-8).toUpperCase(),
      urgency,
      hoursUntil,
      customer: {
        name: booking.user?.fullName || 'Unknown Customer',
        email: booking.user?.email || '',
        phone: booking.user?.phoneNumber || booking.phoneNumber || '',
        image: booking.user?.profileImage || null
      },
      barber: {
        name: booking.barber?.fullName || 'Unknown Barber',
        businessName: booking.barber?.businessName || '',
        email: booking.barber?.email || '',
        phone: booking.barber?.phoneNumber || '',
        image: booking.barber?.profileImage || null
      },
      service: {
        name: booking.service?.name || 'Unknown Service',
        category: booking.service?.category || '',
        duration: booking.service?.duration ? `${booking.service.duration} mins` : 'N/A',
        price: booking.service?.price || booking.totalPrice || 0
      },
      booking: {
        date: formattedDate,
        time: booking.startTime,
        serviceType: booking.serviceType || 'shop',
        address: booking.address || 'Shop location',
        clientType: booking.clientType || 'adult',
        customerCount,
        totalPrice: booking.totalPrice || 0,
        paymentMethod: booking.paymentMethod || 'monnify',
        paymentStatus: booking.paymentStatus || 'pending',
        status: booking.status || 'pending'
      },
      timeline: {
        booked: new Date(booking.createdAt).toLocaleDateString('en-US', {
          month: 'short',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        })
      }
    };
  });

  const totalPages = Math.ceil(total / limit);

  res.status(200).json({
    success: true,
    data: {
      bookings: transformedBookings,
      pagination: {
        totalPages,
        currentPage: page,
        totalBookings: total,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      }
    }
  });
});

// ============================================
// PAYMENT TRANSACTIONS MANAGEMENT
// ============================================

// Get All Payment Transactions (Bookings, Subscriptions, Withdrawals)
exports.getAllPaymentTransactions = catchAsync(async (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 20;
  const searchTerm = req.query.search || '';
  const transactionType = req.query.type || 'all'; // all, booking, subscription, withdrawal
  const skip = (page - 1) * limit;

  console.log(`📊 Fetching payment transactions - Page: ${page}, Type: ${transactionType}, Search: "${searchTerm}"`);

  try {
    let allTransactions = [];

    // 1. Fetch Booking Payments
    if (transactionType === 'all' || transactionType === 'booking') {
      console.log('💳 Fetching booking payments...');
      const bookingPayments = await Payment.find({
        status: { $in: ['held', 'released', 'paid'] }
      })
      .populate('user', 'fullName email phoneNumber')
      .populate('barber', 'fullName businessName email')
      .populate('booking', 'date startTime serviceType')
      .select('amount platformCommission barberAmount status transactionReference paymentReference paidAt createdAt')
      .lean();

      const transformedBookingPayments = bookingPayments.map(payment => ({
        _id: payment._id,
        transactionId: payment.transactionReference,
        paymentReference: payment.paymentReference,
        type: 'booking',
        typeLabel: 'Booking Payment',
        amount: payment.amount,
        platformCommission: payment.platformCommission || 0,
        netAmount: payment.barberAmount || (payment.amount - (payment.platformCommission || 0)),
        status: payment.status,
        date: payment.paidAt || payment.createdAt,
        payer: {
          name: payment.user?.fullName || 'Unknown User',
          email: payment.user?.email || '',
          phone: payment.user?.phoneNumber || ''
        },
        recipient: {
          name: payment.barber?.fullName || 'Unknown Barber',
          businessName: payment.barber?.businessName || '',
          email: payment.barber?.email || ''
        },
        details: {
          bookingDate: payment.booking?.date ? new Date(payment.booking.date).toLocaleDateString() : 'N/A',
          bookingTime: payment.booking?.startTime || 'N/A',
          serviceType: payment.booking?.serviceType || 'N/A'
        },
        createdAt: payment.createdAt
      }));

      allTransactions.push(...transformedBookingPayments);
      console.log(`✅ Found ${transformedBookingPayments.length} booking payments`);
    }

    // 2. Fetch Subscription Payments
    if (transactionType === 'all' || transactionType === 'subscription') {
      console.log('💰 Fetching subscription payments...');
      const subscriptions = await Subscription.find({
        'paymentHistory.paymentStatus': 'completed'
      })
      .populate('barber', 'fullName businessName email phoneNumber')
      .select('barber paymentHistory subscriptionFee')
      .lean();

      const transformedSubscriptionPayments = [];
      subscriptions.forEach(subscription => {
        const completedPayments = subscription.paymentHistory?.filter(payment => 
          payment.paymentStatus === 'completed'
        ) || [];

        completedPayments.forEach(payment => {
          transformedSubscriptionPayments.push({
            _id: `sub_${subscription._id}_${payment._id}`,
            transactionId: payment.transactionReference,
            paymentReference: payment.paymentReference,
            type: 'subscription',
            typeLabel: 'Subscription Payment',
            amount: payment.amount,
            platformCommission: payment.amount, // Full amount goes to platform
            netAmount: 0, // Barber doesn't get money from subscriptions
            status: 'completed',
            date: payment.paymentDate,
            payer: {
              name: subscription.barber?.fullName || 'Unknown Barber',
              email: subscription.barber?.email || '',
              phone: subscription.barber?.phoneNumber || ''
            },
            recipient: {
              name: 'Etch Platform',
              businessName: 'Etch Barber Platform',
              email: '<EMAIL>'
            },
            details: {
              subscriptionPeriod: `${new Date(payment.periodStart).toLocaleDateString()} - ${new Date(payment.periodEnd).toLocaleDateString()}`,
              paymentMethod: payment.paymentMethod || 'monnify'
            },
            createdAt: payment.paymentDate
          });
        });
      });

      allTransactions.push(...transformedSubscriptionPayments);
      console.log(`✅ Found ${transformedSubscriptionPayments.length} subscription payments`);
    }

    // 3. Fetch Completed Withdrawals
    if (transactionType === 'all' || transactionType === 'withdrawal') {
      console.log('🏦 Fetching completed withdrawals...');
      const withdrawals = await Withdrawal.find({
        status: 'COMPLETED'
      })
      .populate('barber', 'fullName businessName email phoneNumber')
      .populate('processedBy', 'fullName email')
      .select('amount status withdrawalReference bankDetails processedAt processedBy createdAt')
      .lean();

      const transformedWithdrawals = withdrawals.map(withdrawal => ({
        _id: withdrawal._id,
        transactionId: withdrawal.withdrawalReference,
        paymentReference: withdrawal.withdrawalReference,
        type: 'withdrawal',
        typeLabel: 'Barber Withdrawal',
        amount: withdrawal.amount,
        platformCommission: 0, // No commission on withdrawals (already deducted from bookings)
        netAmount: withdrawal.amount,
        status: 'completed',
        date: withdrawal.processedAt || withdrawal.createdAt,
        payer: {
          name: 'Etch Platform',
          email: '<EMAIL>',
          phone: ''
        },
        recipient: {
          name: withdrawal.barber?.fullName || 'Unknown Barber',
          businessName: withdrawal.barber?.businessName || '',
          email: withdrawal.barber?.email || ''
        },
        details: {
          bankName: withdrawal.bankDetails?.bankName || 'N/A',
          accountNumber: withdrawal.bankDetails?.accountNumber || 'N/A',
          accountName: withdrawal.bankDetails?.accountName || 'N/A',
          processedBy: withdrawal.processedBy?.fullName || 'System'
        },
        createdAt: withdrawal.createdAt
      }));

      allTransactions.push(...transformedWithdrawals);
      console.log(`✅ Found ${transformedWithdrawals.length} completed withdrawals`);
    }

    // 4. Apply search filter
    if (searchTerm) {
      allTransactions = allTransactions.filter(transaction => {
        const searchLower = searchTerm.toLowerCase();
        return (
          transaction.payer?.name?.toLowerCase().includes(searchLower) ||
          transaction.recipient?.name?.toLowerCase().includes(searchLower) ||
          transaction.transactionId?.toLowerCase().includes(searchLower) ||
          transaction.paymentReference?.toLowerCase().includes(searchLower) ||
          transaction.typeLabel?.toLowerCase().includes(searchLower)
        );
      });
    }

    // 5. Sort by date (newest first)
    allTransactions.sort((a, b) => new Date(b.date) - new Date(a.date));

    // 6. Calculate totals before pagination
    const totalTransactions = allTransactions.length;
    const totalRevenue = allTransactions.reduce((sum, transaction) => sum + (transaction.amount || 0), 0);
    const totalCommissions = allTransactions.reduce((sum, transaction) => sum + (transaction.platformCommission || 0), 0);

    // Count by type
    const typeCounts = {
      booking: allTransactions.filter(t => t.type === 'booking').length,
      subscription: allTransactions.filter(t => t.type === 'subscription').length,
      withdrawal: allTransactions.filter(t => t.type === 'withdrawal').length
    };

    // 7. Apply pagination
    const paginatedTransactions = allTransactions.slice(skip, skip + limit);

    // 8. Format transactions for frontend
    const formattedTransactions = paginatedTransactions.map(transaction => ({
      ...transaction,
      formattedAmount: `₦${transaction.amount.toLocaleString()}`,
      formattedCommission: `₦${(transaction.platformCommission || 0).toLocaleString()}`,
      formattedNetAmount: `₦${(transaction.netAmount || 0).toLocaleString()}`,
      formattedDate: new Date(transaction.date).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      }),
      statusColor: transaction.type === 'withdrawal' ? 'red' : 
                  transaction.type === 'subscription' ? 'blue' : 'green'
    }));

    const totalPages = Math.ceil(totalTransactions / limit);

    console.log(`📊 Summary: ${totalTransactions} total transactions, ${totalPages} pages`);

    res.status(200).json({
      success: true,
      data: {
        transactions: formattedTransactions,
        summary: {
          totalTransactions,
          totalRevenue: Math.round(totalRevenue / 100), // Convert from kobo to naira
          totalCommissions: Math.round(totalCommissions / 100),
          typeCounts,
          currentPageTransactions: formattedTransactions.length
        },
        pagination: {
          totalPages,
          currentPage: page,
          totalTransactions,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1
        }
      }
    });

  } catch (error) {
    console.error('❌ Error fetching payment transactions:', error);
    throw error;
  }
});

// Get User Statistics
const getUserStats = async (req, res) => {
  try {
    const now = new Date();
    const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const firstDayOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const lastDayOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0);

    // Get total verified users (users with status "verified")
    const totalVerifiedUsers = await User.countDocuments({
      status: "verified"
    });

    // Get total users from last month (for growth calculation)
    const lastMonthVerifiedUsers = await User.countDocuments({
      status: "verified",
      createdAt: { $lt: firstDayOfMonth }
    });

    // Calculate growth rate
    const userGrowthRate = lastMonthVerifiedUsers > 0 
      ? ((totalVerifiedUsers - lastMonthVerifiedUsers) / lastMonthVerifiedUsers) * 100 
      : 0;

    // Get all new users this month (regardless of verification status)
    const newUsersThisMonth = await User.countDocuments({
      createdAt: { $gte: firstDayOfMonth }
    });

    // Get all new users last month (regardless of verification status)
    const newUsersLastMonth = await User.countDocuments({
      createdAt: {
        $gte: firstDayOfLastMonth,
        $lte: lastDayOfLastMonth
      }
    });

    // Calculate average bookings per verified user
    const totalBookings = await Booking.countDocuments({
      status: { $in: ['completed', 'confirmed'] },
      user: { 
        $in: await User.distinct('_id', { status: "verified" })
      }
    });

    const verifiedUsersWithBookings = await User.countDocuments({
      status: "verified",
      _id: {
        $in: await Booking.distinct('user', {
          status: { $in: ['completed', 'confirmed'] }
        })
      }
    });

    const averageBookingsPerUser = verifiedUsersWithBookings > 0
      ? (totalBookings / verifiedUsersWithBookings).toFixed(1)
      : 0;

    // Get user activity statistics
    const startOfWeek = new Date();
    startOfWeek.setDate(startOfWeek.getDate() - 7);
    
    const startOfMonth = new Date();
    startOfMonth.setDate(1);

    const startOfToday = new Date();
    startOfToday.setHours(0, 0, 0, 0);

    const dailyActiveUsers = await User.countDocuments({
      status: "verified",
      lastActivity: { $gte: startOfToday }
    });

    const weeklyActiveUsers = await User.countDocuments({
      status: "verified",
      lastActivity: { $gte: startOfWeek }
    });

    const monthlyActiveUsers = await User.countDocuments({
      status: "verified",
      lastActivity: { $gte: startOfMonth }
    });

    res.status(200).json({
      success: true,
      data: {
        totalUsers: totalVerifiedUsers,
        userGrowthRate,
        newUsersThisMonth,
        newUsersLastMonth,
        averageBookingsPerUser,
        userActivity: {
          daily: dailyActiveUsers,
          weekly: weeklyActiveUsers,
          monthly: monthlyActiveUsers
        }
      }
    });

  } catch (error) {
    console.error('Error getting user stats:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching user statistics'
    });
  }
};

// Get Payment Statistics
exports.getPaymentStats = async (req, res) => {
  try {
    const now = new Date();
    const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const firstDayOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const lastDayOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0);

    // Import the models we need
    const Withdrawal = require('../models/Withdrawal');
    const Subscription = require('../models/Subscription');

    // 1. Total Withdrawals from the app (using Withdrawal model)
    const totalWithdrawals = await Withdrawal.aggregate([
      {
        $match: {
          status: 'COMPLETED'
        }
      },
      {
        $group: {
          _id: null,
          totalAmount: { $sum: '$amount' },
          count: { $sum: 1 }
        }
      }
    ]);

    const withdrawalStats = totalWithdrawals[0] || { totalAmount: 0, count: 0 };

    // 2. Total Profit from Commission (Platform earnings from booking payments)
    const commissionEarnings = await Payment.aggregate([
      {
        $match: {
          status: { $in: ['released', 'paid'] }
        }
      },
      {
        $group: {
          _id: null,
          totalCommission: { $sum: '$platformCommission' },
          count: { $sum: 1 }
        }
      }
    ]);

    const commissionStats = commissionEarnings[0] || { totalCommission: 0, count: 0 };

    // 3. Total Revenue from Subscriptions (using Subscription model's paymentHistory)
    const subscriptionRevenue = await Subscription.aggregate([
      {
        $unwind: '$paymentHistory'
      },
      {
        $match: {
          'paymentHistory.paymentStatus': 'completed'
        }
      },
      {
        $group: {
          _id: null,
          totalAmount: { $sum: '$paymentHistory.amount' },
          count: { $sum: 1 }
        }
      }
    ]);

    const subscriptionStats = subscriptionRevenue[0] || { totalAmount: 0, count: 0 };

    // 4. Total Revenue from booking payments (using Payment model)
    const bookingRevenue = await Payment.aggregate([
      {
        $match: {
          status: { $in: ['released', 'paid'] }
        }
      },
      {
        $group: {
          _id: null,
          totalAmount: { $sum: '$amount' },
          count: { $sum: 1 }
        }
      }
    ]);

    const bookingStats = bookingRevenue[0] || { totalAmount: 0, count: 0 };

    // 5. Total Revenue (Booking + Subscription revenue)
    const totalRevenue = bookingStats.totalAmount + subscriptionStats.totalAmount;
    const totalTransactionCount = bookingStats.count + subscriptionStats.count;

    // 6. Monthly Statistics (This month vs Last month)
    
    // This month booking stats
    const thisMonthBookings = await Payment.aggregate([
      {
        $match: {
          createdAt: { $gte: firstDayOfMonth },
          status: { $in: ['released', 'paid'] }
        }
      },
      {
        $group: {
          _id: null,
          totalAmount: { $sum: '$amount' },
          totalCommission: { $sum: '$platformCommission' },
          count: { $sum: 1 }
        }
      }
    ]);

    // Last month booking stats
    const lastMonthBookings = await Payment.aggregate([
      {
        $match: {
          createdAt: {
            $gte: firstDayOfLastMonth,
            $lte: lastDayOfLastMonth
          },
          status: { $in: ['released', 'paid'] }
        }
      },
      {
        $group: {
          _id: null,
          totalAmount: { $sum: '$amount' },
          totalCommission: { $sum: '$platformCommission' },
          count: { $sum: 1 }
        }
      }
    ]);

    // This month subscription stats
    const thisMonthSubscriptions = await Subscription.aggregate([
      {
        $unwind: '$paymentHistory'
      },
      {
        $match: {
          'paymentHistory.paymentDate': { $gte: firstDayOfMonth },
          'paymentHistory.paymentStatus': 'completed'
        }
      },
      {
        $group: {
          _id: null,
          totalAmount: { $sum: '$paymentHistory.amount' },
          count: { $sum: 1 }
        }
      }
    ]);

    // Last month subscription stats
    const lastMonthSubscriptions = await Subscription.aggregate([
      {
        $unwind: '$paymentHistory'
      },
      {
        $match: {
          'paymentHistory.paymentDate': {
            $gte: firstDayOfLastMonth,
            $lte: lastDayOfLastMonth
          },
          'paymentHistory.paymentStatus': 'completed'
        }
      },
      {
        $group: {
          _id: null,
          totalAmount: { $sum: '$paymentHistory.amount' },
          count: { $sum: 1 }
        }
      }
    ]);

    // This month withdrawal stats
    const thisMonthWithdrawals = await Withdrawal.aggregate([
      {
        $match: {
          createdAt: { $gte: firstDayOfMonth },
          status: 'COMPLETED'
        }
      },
      {
        $group: {
          _id: null,
          totalAmount: { $sum: '$amount' },
          count: { $sum: 1 }
        }
      }
    ]);

    // Last month withdrawal stats
    const lastMonthWithdrawals = await Withdrawal.aggregate([
      {
        $match: {
          createdAt: {
            $gte: firstDayOfLastMonth,
            $lte: lastDayOfLastMonth
          },
          status: 'COMPLETED'
        }
      },
      {
        $group: {
          _id: null,
          totalAmount: { $sum: '$amount' },
          count: { $sum: 1 }
        }
      }
    ]);

    // Process monthly data
    const thisMonth = {
      booking: thisMonthBookings[0]?.totalAmount || 0,
      subscription: thisMonthSubscriptions[0]?.totalAmount || 0,
      withdrawal: thisMonthWithdrawals[0]?.totalAmount || 0,
      commission: thisMonthBookings[0]?.totalCommission || 0
    };

    const lastMonth = {
      booking: lastMonthBookings[0]?.totalAmount || 0,
      subscription: lastMonthSubscriptions[0]?.totalAmount || 0,
      withdrawal: lastMonthWithdrawals[0]?.totalAmount || 0,
      commission: lastMonthBookings[0]?.totalCommission || 0
    };

    // Calculate growth rates
    const calculateGrowthRate = (current, previous) => {
      if (previous === 0) return current > 0 ? 100 : 0;
      return ((current - previous) / previous) * 100;
    };

    // 7. Failed Payments Statistics
    const failedBookingPayments = await Payment.countDocuments({
      status: { $in: ['failed', 'cancelled'] }
    });

    const totalBookingPayments = await Payment.countDocuments();
    
    // Count failed subscription payments
    const failedSubscriptionPayments = await Subscription.aggregate([
      {
        $unwind: '$paymentHistory'
      },
      {
        $match: {
          'paymentHistory.paymentStatus': 'failed'
        }
      },
      {
        $count: 'count'
      }
    ]);

    const totalFailedPayments = failedBookingPayments + (failedSubscriptionPayments[0]?.count || 0);
    const totalAllPayments = totalBookingPayments + subscriptionStats.count;
    const successRate = totalAllPayments > 0 ? ((totalAllPayments - totalFailedPayments) / totalAllPayments) * 100 : 100;

    // 8. Average Transaction Values
    const avgBookingValue = bookingStats.count > 0 
      ? bookingStats.totalAmount / bookingStats.count 
      : 0;

    const avgWithdrawalValue = withdrawalStats.count > 0 
      ? withdrawalStats.totalAmount / withdrawalStats.count 
      : 0;

    // 9. Net Platform Profit (Commission earned + Subscription revenue)
    const netPlatformProfit = commissionStats.totalCommission + subscriptionStats.totalAmount;

    res.status(200).json({
      success: true,
      data: {
        // Main Statistics
        totalWithdrawals: Math.round(withdrawalStats.totalAmount / 100), // Convert from kobo
        totalWithdrawalCount: withdrawalStats.count,
        totalCommissionEarned: Math.round(commissionStats.totalCommission / 100),
        totalBookingCommissions: commissionStats.count,
        totalSubscriptionRevenue: Math.round(subscriptionStats.totalAmount / 100),
        totalSubscriptionCount: subscriptionStats.count,
        totalRevenue: Math.round(totalRevenue / 100),
        totalTransactionCount: totalTransactionCount,
        netPlatformProfit: Math.round(netPlatformProfit / 100),

        // Monthly Comparisons
        monthlyGrowth: {
          revenue: calculateGrowthRate(
            thisMonth.booking + thisMonth.subscription,
            lastMonth.booking + lastMonth.subscription
          ),
          commission: calculateGrowthRate(thisMonth.commission, lastMonth.commission),
          subscriptions: calculateGrowthRate(thisMonth.subscription, lastMonth.subscription),
          withdrawals: calculateGrowthRate(thisMonth.withdrawal, lastMonth.withdrawal)
        },

        // Additional Metrics
        paymentSuccessRate: Math.round(successRate),
        failedPaymentCount: totalFailedPayments,
        avgBookingValue: Math.round(avgBookingValue / 100),
        avgWithdrawalValue: Math.round(avgWithdrawalValue / 100),

        // This Month vs Last Month Raw Data
        thisMonthData: {
          revenue: Math.round((thisMonth.booking + thisMonth.subscription) / 100),
          commission: Math.round(thisMonth.commission / 100),
          subscriptions: Math.round(thisMonth.subscription / 100),
          withdrawals: Math.round(thisMonth.withdrawal / 100)
        },
        lastMonthData: {
          revenue: Math.round((lastMonth.booking + lastMonth.subscription) / 100),
          commission: Math.round(lastMonth.commission / 100),
          subscriptions: Math.round(lastMonth.subscription / 100),
          withdrawals: Math.round(lastMonth.withdrawal / 100)
        }
      }
    });

  } catch (error) {
    console.error('Error getting payment stats:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching payment statistics'
    });
  }
};

module.exports = {
  adminLogin,
  adminLogout,
  getAdminProfile,
  updateAdminProfile,
  requestAdminPasswordReset,
  resetAdminPassword,
  changeAdminPassword,
  getVerificationRequests,
  getVerificationRequestDetails,
  assignVerificationRequest,
  startVerificationReview,
  verifyDocument,
  completeVerificationReview,
  requestClarification,
  getUserOverviewStats: exports.getUserOverviewStats,
  getUsers: exports.getUsers,
  getUserProfile: exports.getUserProfile,
  updateUserStatus: exports.updateUserStatus,
  sendBulkEmail: exports.sendBulkEmail,
  updateVerificationStatus: exports.updateVerificationStatus,
  getDashboardStats: exports.getDashboardStats,
  getRecentActivity: exports.getRecentActivity,
  getBarberStats: exports.getBarberStats,
  deleteUser: exports.deleteUser,
  approveVerificationRequest,
  rejectVerificationRequest,
  getVerifiedBarbers: exports.getVerifiedBarbers,
  getAllCompletedBookings: exports.getAllCompletedBookings,
  getAllBookings: exports.getAllBookings,
  getPendingBookings: exports.getPendingBookings,
  getAllPaymentTransactions: exports.getAllPaymentTransactions,
  getUserStats: getUserStats,
  getPaymentStats: exports.getPaymentStats
};
