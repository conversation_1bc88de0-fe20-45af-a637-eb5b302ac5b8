const Subscription = require('../models/Subscription');
const Barber = require('../models/Barber');
const { catchAsync, AppError } = require('../utils/errorHandlers');
const paymentService = require('../utils/paymentService');
const emailService = require('../utils/emailService');
const paymentConfig = require('../config/payment');

// Get barber's subscription status
exports.getSubscriptionStatus = catchAsync(async (req, res, next) => {
  try {
    console.log('Getting subscription status for barber:', req.user._id);
    
    const barberId = req.user._id;
    
    if (!barberId) {
      return next(new AppError('Barber ID not found in request', 400));
    }
    
    const subscription = await Subscription.getOrCreate(barberId);
    
    console.log('Found subscription:', {
      id: subscription._id,
      status: subscription.status,
      isActive: subscription.isActive,
      hasPaymentHistory: !!subscription.paymentHistory,
      paymentHistoryLength: subscription.paymentHistory ? subscription.paymentHistory.length : 0
    });
    
    // Ensure all fields are safe before sending
    const safeSubscription = {
      id: subscription._id,
      status: subscription.status || 'never_subscribed',
      isActive: subscription.isActive || false,
      daysRemaining: subscription.daysRemaining || 0,
      currentPeriod: subscription.currentPeriod || { startDate: null, endDate: null },
      subscriptionFee: paymentConfig.subscriptionFee,
      lastRenewalDate: subscription.lastRenewalDate || null,
      paymentHistory: subscription.paymentHistory ? subscription.paymentHistory.slice(-5) : []
    };
    
    console.log('Sending safe subscription data:', safeSubscription);
    
    res.status(200).json({
      success: true,
      data: {
        subscription: safeSubscription
      }
    });
  } catch (error) {
    console.error('Error in getSubscriptionStatus:', error);
    return next(new AppError('Error fetching subscription status: ' + error.message, 500));
  }
});

// Initiate subscription payment
exports.initiateSubscriptionPayment = catchAsync(async (req, res, next) => {
  const barberId = req.user._id;
  
  // Get or create subscription
  const subscription = await Subscription.getOrCreate(barberId);
  
  // Get barber details
  const barber = await Barber.findById(barberId);
  if (!barber) {
    return next(new AppError('Barber not found', 404));
  }
  
  // Initialize payment with Monnify using current fee from config
  const paymentData = await paymentService.initializeSubscriptionTransaction({
    barberId: barberId,
    customerEmail: barber.email,
    customerName: barber.fullName
  });
  
  res.status(200).json({
    success: true,
    message: 'Subscription payment initialized successfully',
    data: paymentData
  });
});

// Handle successful subscription payment
exports.handleSubscriptionPaymentSuccess = catchAsync(async (req, res, next) => {
  const { transactionReference, paymentReference, subscriptionId } = req.body;
  
  if (!transactionReference || !paymentReference || !subscriptionId) {
    return next(new AppError('Missing required payment data', 400));
  }
  
  // Verify payment with Monnify
  const paymentVerification = await paymentService.verifyTransaction(transactionReference);
  
  if (paymentVerification.paymentStatus !== 'PAID') {
    return next(new AppError('Payment verification failed', 400));
  }
  
  // Get subscription and activate it
  const subscription = await Subscription.findById(subscriptionId).populate('barber');
  if (!subscription) {
    return next(new AppError('Subscription not found', 404));
  }
  
  // Activate subscription
  subscription.activateSubscription({
    transactionReference,
    paymentReference,
    amount: paymentVerification.amountPaid,
    paymentDate: new Date()
  });
  
  await subscription.save();
  
  // Note: We enable profile toggle functionality but don't automatically turn ON the profile
  // The barber can choose to make their profile visible after subscription activation
  const barber = subscription.barber;
  
  // Send success email
  try {
    await emailService.sendSubscriptionActivationEmail(
      barber.email,
      barber.fullName,
      {
        amount: subscription.subscriptionFee,
        startDate: subscription.currentPeriod.startDate,
        endDate: subscription.currentPeriod.endDate,
        transactionReference
      }
    );
  } catch (emailError) {
    console.error('Failed to send subscription activation email:', emailError);
  }
  
  res.status(200).json({
    success: true,
    message: 'Subscription activated successfully! You can now make your profile visible to customers.',
    data: {
      subscription: {
        id: subscription._id,
        status: subscription.status,
        isActive: subscription.isActive,
        daysRemaining: subscription.daysRemaining,
        currentPeriod: subscription.currentPeriod
      },
      profileToggleEnabled: true,
      nextAction: 'Visit your profile settings to make your profile visible to customers'
    }
  });
});

// Check if barber can toggle profile visibility
exports.checkProfileTogglePermission = catchAsync(async (req, res, next) => {
  console.log('Checking profile toggle permission for barber:', req.user._id);
  
  const barberId = req.user._id;
  
  if (!barberId) {
    return next(new AppError('Barber ID not found in request', 400));
  }
  
  const subscription = await Subscription.getOrCreate(barberId);
  
  const canToggle = subscription.isActive;
  let reason = null;
  
  if (!canToggle) {
    if (subscription.status === 'never_subscribed') {
      reason = `Subscribe for ₦${paymentConfig.subscriptionFee.toLocaleString()}/month to activate your profile.`;
    } else if (subscription.status === 'expired') {
      reason = `Your subscription has expired. Please renew your ₦${paymentConfig.subscriptionFee.toLocaleString()} monthly subscription to make your profile visible.`;
    } else if (subscription.status === 'suspended') {
      reason = 'Your subscription is suspended. Please contact support to reactivate your account.';
    } else if (subscription.status === 'cancelled') {
      reason = 'Your subscription was cancelled. Please renew your subscription to make your profile visible.';
    } else {
      reason = 'Active subscription required to make your profile visible to customers.';
    }
  }
  
  console.log('Profile toggle permission:', {
    canToggle,
    subscriptionStatus: subscription.status,
    daysRemaining: subscription.daysRemaining
  });
  
  res.status(200).json({
    success: true,
    data: {
      canToggle,
      reason,
      subscriptionStatus: subscription.status,
      daysRemaining: subscription.daysRemaining
    }
  });
});

// Get subscription payment history
exports.getPaymentHistory = catchAsync(async (req, res, next) => {
  const barberId = req.user._id;
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10;
  
  const subscription = await Subscription.findOne({ barber: barberId });
  
  if (!subscription) {
    return res.status(200).json({
      success: true,
      data: {
        payments: [],
        pagination: {
          currentPage: page,
          totalPages: 0,
          totalItems: 0,
          itemsPerPage: limit
        }
      }
    });
  }
  
  const paymentHistory = subscription.paymentHistory || [];
  const payments = paymentHistory
    .sort((a, b) => new Date(b.paymentDate) - new Date(a.paymentDate))
    .slice((page - 1) * limit, page * limit);
  
  const totalItems = paymentHistory.length;
  
  res.status(200).json({
    success: true,
    data: {
      payments,
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(totalItems / limit),
        totalItems,
        itemsPerPage: limit
      }
    }
  });
});

// Verify subscription payment status (new endpoint for the flow)
exports.verifySubscription = catchAsync(async (req, res, next) => {
  const { transactionReference, paymentReference } = req.body;
  
  // Accept either transaction reference or payment reference
  const referenceToVerify = transactionReference || paymentReference;
  
  if (!referenceToVerify) {
    return next(new AppError('Transaction reference or payment reference is required', 400));
  }
  
  console.log('Verifying subscription with reference:', referenceToVerify);
  
  try {
    // Get barber ID from authenticated user
    const barberId = req.user._id;
    
    // Call Monnify Get Transaction Status API
    const monnifyData = await paymentService.verifyTransaction(referenceToVerify);
    console.log('Monnify verification result:', monnifyData);
    
    if (monnifyData.paymentStatus === 'PAID') {
      // Extract card token from response
      // Note: The card token might be in different fields depending on Monnify's response structure
      const cardToken = monnifyData.cardToken || monnifyData.card?.token || monnifyData.tokenizedCard || null;
      console.log('Card token received:', !!cardToken);
      console.log('Monnify response for token extraction:', {
        hasCardToken: !!monnifyData.cardToken,
        hasCardObject: !!monnifyData.card,
        hasTokenizedCard: !!monnifyData.tokenizedCard
      });
      
      // Get subscription and activate it
      const subscription = await Subscription.getOrCreate(barberId);
      
      // Activate subscription with card token
      subscription.activateSubscription({
        transactionReference: monnifyData.transactionReference || referenceToVerify,
        paymentReference: referenceToVerify,
        amount: monnifyData.amountPaid || paymentConfig.subscriptionFee,
        paymentDate: new Date(),
        cardToken: cardToken,
        isRecurring: false
      });
      
      await subscription.save();
      
      // Send success email
      const barber = await Barber.findById(barberId);
      try {
        await emailService.sendSubscriptionActivationEmail(
          barber.email,
          barber.fullName,
          {
            amount: subscription.subscriptionFee,
            startDate: subscription.currentPeriod.startDate,
            endDate: subscription.currentPeriod.endDate,
            transactionReference: monnifyData.transactionReference || referenceToVerify
          }
        );
      } catch (emailError) {
        console.error('Failed to send subscription activation email:', emailError);
      }
      
      res.status(200).json({
        success: true,
        status: 'success',
        message: 'Subscription activated successfully!',
        data: {
          subscription: {
            id: subscription._id,
            status: subscription.status,
            isActive: subscription.isActive,
            daysRemaining: subscription.daysRemaining,
            currentPeriod: subscription.currentPeriod,
            recurringEnabled: subscription.recurringBilling.enabled
          }
        }
      });
    } else if (monnifyData.paymentStatus === 'FAILED') {
      // Update subscription status to failed
      const subscription = await Subscription.getOrCreate(barberId);
      subscription.status = 'never_subscribed';
      await subscription.save();
      
      res.status(200).json({
        success: false,
        status: 'failed',
        message: 'Payment failed'
      });
    } else if (monnifyData.paymentStatus === 'CANCELLED') {
      // Update subscription status 
      const subscription = await Subscription.getOrCreate(barberId);
      subscription.status = 'never_subscribed';
      await subscription.save();
      
      res.status(200).json({
        success: false,
        status: 'cancelled',
        message: 'Payment was cancelled'
      });
    } else if (monnifyData.paymentStatus === 'NOT_FOUND') {
      // Transaction not found - likely cancelled before completion or payment window closed
      const subscription = await Subscription.getOrCreate(barberId);
      subscription.status = 'never_subscribed';
      await subscription.save();
      
      res.status(200).json({
        success: false,
        status: 'cancelled',
        message: 'Payment session was not completed. Please try again.'
      });
    } else {
      res.status(200).json({
        success: false,
        status: 'pending',
        message: 'Payment is still being processed'
      });
    }
  } catch (error) {
    console.error('Subscription verification failed:', error);
    return next(new AppError('Subscription verification failed: ' + error.message, 500));
  }
});

// Verify pending subscription without specific reference
exports.verifyPendingSubscription = catchAsync(async (req, res, next) => {
  console.log('Verifying pending subscription for barber:', req.user._id);
  
  try {
    const barberId = req.user._id;
    
    // Get the subscription for this barber
    const subscription = await Subscription.getOrCreate(barberId);
    
    if (subscription.status !== 'pending') {
      return res.status(200).json({
        success: false,
        status: 'no_pending',
        message: 'No pending subscription found'
      });
    }
    
    console.log('Found pending subscription, checking for recent transactions...');
    
    // Look for recent transactions (last 10 minutes) that might be for this barber
    // We'll check Monnify for transactions with our contract code
    const paymentService = require('../utils/paymentService');
    
    // Since we don't have a specific reference, we'll need to check if there are any
    // recent successful transactions for this barber
    // For now, let's check if the subscription was recently updated to pending
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
    
    if (subscription.updatedAt > fiveMinutesAgo) {
      console.log('Subscription was recently set to pending, likely payment was successful');
      
      // Try to find any recent payment references in our logs or generate a check
      // For now, we'll return that we couldn't find a specific transaction
      return res.status(200).json({
        success: false,
        status: 'verification_needed',
        message: 'Pending subscription found but no transaction reference available. Please contact support if payment was successful.'
      });
    } else {
      // Subscription has been pending for a while, likely payment failed or was cancelled
      console.log('Subscription has been pending for more than 5 minutes, likely failed');
      
      // Reset subscription status
      subscription.status = 'never_subscribed';
      await subscription.save();
      
      return res.status(200).json({
        success: false,
        status: 'expired_pending',
        message: 'Pending payment session expired. Please try making the payment again.'
      });
    }
    
  } catch (error) {
    console.error('Pending subscription verification failed:', error);
    return next(new AppError('Pending subscription verification failed: ' + error.message, 500));
  }
});

// Verify subscription payment status (existing endpoint for compatibility)
exports.verifySubscriptionPayment = catchAsync(async (req, res, next) => {
  const { paymentReference } = req.params;
  
  // Basic validation of payment reference format
  if (!paymentReference || !paymentReference.startsWith('ETCH-')) {
    return next(new AppError('Invalid payment reference format', 400));
  }
  
  console.log('Verifying subscription payment for reference:', paymentReference);
  
  try {
    // Check if this is an authenticated request (barber verifying their own payment)
    let barberId = null;
    if (req.user && req.user._id) {
      barberId = req.user._id;
      console.log('Authenticated barber verification for:', barberId);
    }
    
    // Verify payment with payment service, passing barber ID if available
    const verificationResult = await paymentService.verifySubscriptionPayment(paymentReference, barberId);
    
    console.log('Subscription verification result:', verificationResult);
    
    if (verificationResult.success) {
      res.status(200).json({
        success: true,
        status: 'success',
        data: verificationResult.data
      });
    } else {
      res.status(200).json({
        success: false,
        status: verificationResult.status,
        message: verificationResult.message
      });
    }
  } catch (error) {
    console.error('Subscription payment verification failed:', error);
    return next(new AppError('Subscription payment verification failed: ' + error.message, 500));
  }
});

// Cancel subscription payment
exports.cancelSubscriptionPayment = catchAsync(async (req, res, next) => {
  const { paymentReference } = req.params;
  
  try {
    const result = await paymentService.cancelSubscriptionPayment(paymentReference);
    
    res.status(200).json({
      success: true,
      message: 'Subscription payment cancelled successfully',
      data: result
    });
  } catch (error) {
    console.error('Subscription payment cancellation failed:', error);
    res.status(400).json({
      success: false,
      message: error.message || 'Failed to cancel subscription payment'
    });
  }
});

// Admin: Get all subscriptions with filters
exports.getAllSubscriptions = catchAsync(async (req, res, next) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 20;
  const status = req.query.status;
  const skip = (page - 1) * limit;
  
  let query = {};
  if (status && status !== 'all') {
    query.status = status;
  }
  
  const [subscriptions, total] = await Promise.all([
    Subscription.find(query)
      .populate('barber', 'fullName email businessName createdAt')
      .skip(skip)
      .limit(limit)
      .sort({ updatedAt: -1 }),
    Subscription.countDocuments(query)
  ]);
  
  res.status(200).json({
    success: true,
    data: {
      subscriptions,
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(total / limit),
        totalItems: total,
        itemsPerPage: limit
      }
    }
  });
});

// Admin: Update subscription status
exports.updateSubscriptionStatus = catchAsync(async (req, res, next) => {
  const { subscriptionId } = req.params;
  const { status, reason } = req.body;
  
  const subscription = await Subscription.findById(subscriptionId).populate('barber');
  
  if (!subscription) {
    return next(new AppError('Subscription not found', 404));
  }
  
  const previousStatus = subscription.status;
  subscription.status = status;
  
  if (status === 'suspended' && reason) {
    subscription.suspensionReason = reason;
  }
  
  await subscription.save();
  
  // Update barber profile visibility if subscription is expired/suspended
  if (['expired', 'suspended', 'cancelled'].includes(status)) {
    await Barber.findByIdAndUpdate(subscription.barber._id, {
      isProfileActive: false
    });
  }
  
  // Send notification email
  try {
    await emailService.sendSubscriptionStatusChangeEmail(
      subscription.barber.email,
      subscription.barber.fullName,
      {
        previousStatus,
        newStatus: status,
        reason
      }
    );
  } catch (emailError) {
    console.error('Failed to send subscription status change email:', emailError);
  }
  
  res.status(200).json({
    success: true,
    message: `Subscription ${status} successfully`,
    data: {
      subscription
    }
  });
});

// Process expired subscriptions (used by cron job)
exports.processExpiredSubscriptions = catchAsync(async (req, res, next) => {
  try {
    const expiredSubscriptions = await Subscription.getExpiredSubscriptions();
    
    let processedCount = 0;
    
    for (const subscription of expiredSubscriptions) {
      // Update subscription status
      subscription.status = 'expired';
      await subscription.save();
      
      // Disable barber profile visibility
      await Barber.findByIdAndUpdate(subscription.barber._id, {
        isProfileActive: false
      });
      
      // Send expiry notification
      try {
        await emailService.sendSubscriptionExpiredEmail(
          subscription.barber.email,
          subscription.barber.fullName,
          {
            expiredDate: subscription.currentPeriod.endDate,
            renewalUrl: `${process.env.CLIENT_URL}/barber/subscription`
          }
        );
        
        // Mark reminder as sent
        subscription.markReminderSent('expired');
        await subscription.save();
      } catch (emailError) {
        console.error('Failed to send expiry email:', emailError);
      }
      
      processedCount++;
    }
    
    if (req.url) {
      // Called via API
      res.status(200).json({
        success: true,
        message: `Processed ${processedCount} expired subscriptions`,
        data: {
          processedCount,
          expiredSubscriptions: expiredSubscriptions.length
        }
      });
    } else {
      // Called by cron job
      console.log(`Processed ${processedCount} expired subscriptions`);
      return processedCount;
    }
  } catch (error) {
    console.error('Error processing expired subscriptions:', error);
    if (req.url) {
      return next(new AppError('Failed to process expired subscriptions', 500));
    }
    throw error;
  }
});

// Send renewal reminders (used by cron job)
exports.sendRenewalReminders = catchAsync(async (req, res, next) => {
  try {
    const subscriptionsNeedingReminders = await Subscription.getSubscriptionsNeedingReminders();
    
    let remindersSent = 0;
    
    for (const subscription of subscriptionsNeedingReminders) {
      const daysRemaining = subscription.daysRemaining;
      
      let reminderType;
      if (daysRemaining <= 3 && daysRemaining > 0) {
        reminderType = '3_days_before';
      } else if (daysRemaining <= 0 && daysRemaining > -1) {
        reminderType = 'expiry_day';
      } else {
        continue;
      }
      
      if (subscription.shouldSendReminder(reminderType)) {
        try {
          await emailService.sendSubscriptionReminderEmail(
            subscription.barber.email,
            subscription.barber.fullName,
            {
              reminderType,
              daysRemaining,
              expiryDate: subscription.currentPeriod.endDate,
              renewalUrl: `${process.env.CLIENT_URL}/barber/subscription`
            }
          );
          
          subscription.markReminderSent(reminderType);
          await subscription.save();
          
          remindersSent++;
        } catch (emailError) {
          console.error('Failed to send renewal reminder:', emailError);
        }
      }
    }
    
    if (req.url) {
      // Called via API
      res.status(200).json({
        success: true,
        message: `Sent ${remindersSent} renewal reminders`,
        data: {
          remindersSent
        }
      });
    } else {
      // Called by cron job
      console.log(`Sent ${remindersSent} renewal reminders`);
      return remindersSent;
    }
  } catch (error) {
    console.error('Error sending renewal reminders:', error);
    if (req.url) {
      return next(new AppError('Failed to send renewal reminders', 500));
    }
    throw error;
  }
});

// Test endpoint to check if authentication is working
exports.testAuth = catchAsync(async (req, res, next) => {
  console.log('Test auth endpoint hit by user:', {
    userId: req.user._id,
    role: req.user.role,
    email: req.user.email
  });
  
  res.status(200).json({
    success: true,
    message: 'Authentication working correctly',
    data: {
      userId: req.user._id,
      role: req.user.role,
      email: req.user.email
    }
  });
}); 

// Webhook handler for subscription events
exports.handleSubscriptionWebhook = catchAsync(async (req, res, next) => {
  console.log('Received subscription webhook:', req.body);
  
  try {
    const { eventType, eventData } = req.body;
    
    switch (eventType) {
      case 'SUCCESSFUL_TRANSACTION':
        if (eventData.metaData?.type === 'recurring_subscription') {
          await handleRecurringPaymentSuccess(eventData);
        }
        break;
        
      case 'FAILED_TRANSACTION':
        if (eventData.metaData?.type === 'recurring_subscription') {
          await handleRecurringPaymentFailure(eventData);
        }
        break;
        
      case 'MANDATE_STATUS_CHANGE':
        await handleMandateStatusChange(eventData);
        break;
        
      default:
        console.log('Unhandled subscription webhook event type:', eventType);
    }
    
    res.status(200).json({
      success: true,
      message: 'Webhook processed successfully'
    });
  } catch (error) {
    console.error('Subscription webhook processing failed:', error);
    res.status(500).json({
      success: false,
      message: 'Webhook processing failed',
      error: error.message
    });
  }
});

// Handle recurring payment success
const handleRecurringPaymentSuccess = async (eventData) => {
  const { transactionReference, amountPaid, customerEmail, metaData } = eventData;
  const barberId = metaData?.barberId;
  
  if (!barberId) {
    console.error('No barber ID found in recurring payment webhook');
    return;
  }
  
  console.log(`Processing recurring payment success for barber: ${barberId}`);
  
  const subscription = await Subscription.findOne({ barber: barberId }).populate('barber');
  if (!subscription) {
    console.error(`Subscription not found for barber: ${barberId}`);
    return;
  }
  
  // Process successful recurring payment
  subscription.processRecurringPaymentSuccess({
    transactionReference,
    paymentReference: transactionReference,
    amount: amountPaid,
    paymentDate: new Date()
  });
  
  await subscription.save();
  
  // Send renewal confirmation email
  try {
    await emailService.sendSubscriptionRenewalEmail(
      subscription.barber.email,
      subscription.barber.fullName,
      {
        amount: amountPaid,
        startDate: subscription.currentPeriod.startDate,
        endDate: subscription.currentPeriod.endDate,
        transactionReference
      }
    );
  } catch (emailError) {
    console.error('Failed to send renewal email:', emailError);
  }
  
  console.log(`Recurring payment processed successfully for barber: ${barberId}`);
};

// Handle recurring payment failure
const handleRecurringPaymentFailure = async (eventData) => {
  const { transactionReference, customerEmail, metaData, failureReason } = eventData;
  const barberId = metaData?.barberId;
  
  if (!barberId) {
    console.error('No barber ID found in recurring payment failure webhook');
    return;
  }
  
  console.log(`Processing recurring payment failure for barber: ${barberId}`);
  
  const subscription = await Subscription.findOne({ barber: barberId }).populate('barber');
  if (!subscription) {
    console.error(`Subscription not found for barber: ${barberId}`);
    return;
  }
  
  // Process failed recurring payment
  subscription.processRecurringPaymentFailure({
    transactionReference,
    paymentReference: transactionReference,
    amount: subscription.subscriptionFee,
    failureDate: new Date(),
    reason: failureReason
  });
  
  await subscription.save();
  
  // Send failure notification email
  try {
    const isMaxRetriesReached = subscription.recurringBilling.failedAttempts >= subscription.recurringBilling.maxRetries;
    
    await emailService.sendRecurringPaymentFailureEmail(
      subscription.barber.email,
      subscription.barber.fullName,
      {
        failedAttempts: subscription.recurringBilling.failedAttempts,
        maxRetries: subscription.recurringBilling.maxRetries,
        isMaxRetriesReached,
        renewalUrl: `${process.env.CLIENT_URL}/barber/subscription`
      }
    );
  } catch (emailError) {
    console.error('Failed to send payment failure email:', emailError);
  }
  
  console.log(`Recurring payment failure processed for barber: ${barberId}`);
};

// Handle mandate status changes
const handleMandateStatusChange = async (eventData) => {
  const { mandateReference, status, customerEmail } = eventData;
  
  console.log(`Mandate status change: ${mandateReference} -> ${status}`);
  
  // Find subscription by card token or customer email
  const subscription = await Subscription.findOne({
    $or: [
      { cardToken: mandateReference },
      { 'barber.email': customerEmail }
    ]
  }).populate('barber');
  
  if (!subscription) {
    console.error(`Subscription not found for mandate: ${mandateReference}`);
    return;
  }
  
  if (status === 'CANCELLED' || status === 'EXPIRED') {
    // Disable recurring billing
    subscription.recurringBilling.enabled = false;
    subscription.cardToken = null;
    await subscription.save();
    
    console.log(`Recurring billing disabled for subscription: ${subscription._id}`);
  }
}; 