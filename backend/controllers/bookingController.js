const Booking = require('../models/Booking');
const Service = require('../models/Service');
const Schedule = require('../models/Schedule');
const { catchAsync, AppError } = require('../utils/errorHandlers');
const paymentService = require('../utils/paymentService');
const notificationService = require('../utils/notificationService');
const Barber = require('../models/Barber');

exports.checkTimeSlotAvailability = catchAsync(async (req, res, next) => {
  const { barberId, date, startTime } = req.query;
  
  if (!barberId || !date || !startTime) {
    return next(new AppError('Missing required parameters', 400));
  }

  // Parse the date and time
  const [hours, minutes] = startTime.split(':').map(Number);
  const requestedDateTime = new Date(date);
  requestedDateTime.setHours(hours, minutes, 0, 0);

  // Calculate the one-hour window for this booking
  const oneHourAfter = new Date(requestedDateTime);
  oneHourAfter.setHours(oneHourAfter.getHours() + 1);

  // Find any conflicting bookings in the same one-hour window
  const conflictingBooking = await Booking.findOne({
    barber: barberId,
    // Only check active bookings
    status: { $in: ['pending', 'confirmed', 'in_progress'] },
    completedAt: null,
    // Check if the booking overlaps with our one-hour window
    date: {
      $gte: new Date(requestedDateTime.getTime() - 3600000), // 1 hour before
      $lt: oneHourAfter // Up to 1 hour after start time
    }
  });

  if (conflictingBooking) {
    return res.status(200).json({
      success: true,
      data: { 
        available: false
      }
    });
  }

  // If no conflicts found, the time slot is available
  res.status(200).json({
    success: true,
    data: { 
      available: true
    }
  });
});

exports.getAvailableTimeSlots = catchAsync(async (req, res, next) => {
  const { barberId, date } = req.query;
  
  if (!barberId || !date) {
    return next(new AppError('Missing required parameters', 400));
  }

  // Get barber's schedule for the day
  const dayOfWeek = new Date(date).getDay();
  const schedule = await Schedule.findOne({ 
    barber: barberId,
    dayOfWeek
  });

  if (!schedule || !schedule.isAvailable) {
    return next(new AppError('Barber is not available on this day', 400));
  }

  // Check for special dates
  const specialDate = schedule.specialDates.find(
    sd => sd.date.toDateString() === new Date(date).toDateString()
  );
  
  if (specialDate && !specialDate.isAvailable) {
    return next(new AppError('Barber is not available on this date', 400));
  }

  // Get existing bookings for the day
  const searchDate = new Date(date);
  const dayStart = new Date(searchDate.setHours(0, 0, 0));
  const dayEnd = new Date(searchDate.setHours(23, 59, 59));

  // Get all uncompleted bookings
  const existingBookings = await Booking.find({
    barber: barberId,
    date: {
      $gte: dayStart,
      $lt: dayEnd
    },
    status: { $nin: ['completed', 'cancelled', 'no-show'] }
  }).sort('date');

  // Calculate available time slots
  const availableSlots = [];
  const startTime = specialDate?.startTime || schedule.startTime;
  const endTime = specialDate?.endTime || schedule.endTime;
  
  // Convert schedule times to Date objects for easier comparison
  const [startHour, startMinute] = startTime.split(':').map(Number);
  const [endHour, endMinute] = endTime.split(':').map(Number);
  
  // Generate slots in 30-minute intervals
  let currentTime = new Date(date);
  currentTime.setHours(startHour, startMinute, 0, 0);
  
  const endDateTime = new Date(date);
  endDateTime.setHours(endHour, endMinute, 0, 0);

  while (currentTime < endDateTime) {
    const slotStart = new Date(currentTime);
    const slotEnd = new Date(slotStart);
    slotEnd.setHours(slotEnd.getHours() + 1); // One hour window

    // Check if slot overlaps with any existing booking's one-hour window
    const isConflicting = existingBookings.some(booking => {
      const bookingStart = new Date(booking.date);
      const bookingEnd = new Date(bookingStart);
      bookingEnd.setHours(bookingEnd.getHours() + 1);

      return (slotStart < bookingEnd && slotEnd > bookingStart);
    });

    // Check if slot overlaps with break times
    const timeString = `${String(slotStart.getHours()).padStart(2, '0')}:${String(slotStart.getMinutes()).padStart(2, '0')}`;
    const slotEndString = `${String(slotEnd.getHours()).padStart(2, '0')}:${String(slotEnd.getMinutes()).padStart(2, '0')}`;
    
    const isBreakTime = schedule.breakTimes.some(break_ => {
      return (timeString >= break_.startTime && timeString < break_.endTime) ||
             (slotEndString > break_.startTime && slotEndString <= break_.endTime);
    });

    if (!isConflicting && !isBreakTime && slotEnd <= endDateTime) {
      availableSlots.push({
        startTime: timeString,
        endTime: slotEndString
      });
    }

    // Move to next slot (30-minute intervals)
    currentTime.setMinutes(currentTime.getMinutes() + 30);
  }

  res.status(200).json({
    success: true,
    data: { availableSlots }
  });
});

exports.createBooking = catchAsync(async (req, res, next) => {
  const { barberId, serviceId, date, startTime, clientType, totalPrice, paymentMethod, phoneNumber, address, serviceType, numAdults, numKids } = req.body;
  
  // Validate service and get duration
  const service = await Service.findById(serviceId).populate('barber');
  if (!service) {
    return next(new AppError('Service not found', 404));
  }

  // Calculate end time
  const [hours, minutes] = startTime.split(':').map(Number);
  const startDateTime = new Date(date);
  startDateTime.setHours(hours, minutes, 0, 0);
  
  const endDateTime = new Date(startDateTime);
  endDateTime.setMinutes(endDateTime.getMinutes() + service.duration);

  // Validate booking time is in the future
  if (startDateTime <= new Date()) {
    return next(new AppError('Booking time must be in the future', 400));
  }

  // Calculate buffer window (1 hour before and after)
  const bufferStart = new Date(startDateTime);
  bufferStart.setHours(bufferStart.getHours() - 1);
  
  const bufferEnd = new Date(endDateTime);
  bufferEnd.setHours(bufferEnd.getHours() + 1);

  // Check for conflicts within buffer window
  const conflictingBooking = await Booking.findOne({
    barber: barberId,
    status: { $in: ['pending', 'confirmed'] },
    $or: [
      // Check if new booking's buffer overlaps with existing booking
      {
        date: {
          $gte: bufferStart,
          $lt: bufferEnd
        }
      },
      // Check if existing booking's buffer overlaps with new booking
      {
        $and: [
          { date: { $lte: startDateTime } },
          { endTime: { $gt: startDateTime } }
        ]
      }
    ]
  });

  if (conflictingBooking) {
    return next(new AppError('This time slot is not available due to nearby bookings. Please select a time at least 1 hour apart from other bookings.', 400));
  }

  // Validate service type requirements
  if (serviceType === 'home' && (!phoneNumber || !address)) {
    return next(new AppError('Phone number and address are required for home service', 400));
  }

  // Validate number of people
  const adults = numAdults || 1;
  const kids = numKids || 0;
  
  if (adults + kids === 0) {
    return next(new AppError('At least one person must be specified for the booking', 400));
  }

  // Determine client type based on numbers
  let finalClientType = clientType;
  if (!finalClientType) {
    if (kids > 0 && adults > 0) {
      finalClientType = 'mixed';
    } else if (kids > 0) {
      finalClientType = 'kid';
    } else {
      finalClientType = 'adult';
    }
  }

  // Create booking with pending status
  const booking = new Booking({
    user: req.user.id,
    barber: barberId,
    service: serviceId,
    serviceType,
    date: startDateTime,
    startTime,
    endTime: endDateTime.toTimeString().slice(0, 5),
    clientType: finalClientType,
    numAdults: adults,
    numKids: kids,
    totalPrice,
    paymentMethod,
    phoneNumber,
    address,
    status: 'pending',
    paymentStatus: 'pending'
  });

  await booking.save();

  // If payment method is Monnify, initialize payment
  if (paymentMethod === 'monnify') {
    try {
      const paymentData = await paymentService.initializeTransaction({
        amount: totalPrice,
        userId: req.user.id,
        barberId,
        bookingId: booking._id,
        customerEmail: req.user.email,
        customerName: req.user.name
      });

      // Update booking with payment reference
      booking.paymentReference = paymentData.paymentReference;
      booking.transactionReference = paymentData.transactionReference;
      booking.paymentStatus = 'processing';
      await booking.save();

      // Populate booking data
      await booking.populate([
        { path: 'user', select: 'name email phone' },
        { path: 'barber', select: 'name email phone businessName' },
        { path: 'service', select: 'name price duration description' }
      ]);

      res.status(201).json({
        status: 'success',
        data: {
          booking,
          payment: paymentData
        }
      });
    } catch (paymentError) {
      // If payment initialization fails, delete the booking
      await Booking.findByIdAndDelete(booking._id);
      return next(new AppError('Payment initialization failed', 500));
    }
  } else {
    // For other payment methods (like cash), just return the booking
    await booking.populate([
      { path: 'user', select: 'name email phone' },
      { path: 'barber', select: 'name email phone businessName' },
      { path: 'service', select: 'name price duration description' }
    ]);

    res.status(201).json({
      status: 'success',
      data: {
        booking
      }
    });
  }
});

exports.handlePaymentWebhook = catchAsync(async (req, res, next) => {
  const { paymentReference, status } = req.body;

  const booking = await Booking.findOne({
    'paymentDetails.paymentReference': paymentReference
  }).populate('user barber service');

  if (!booking) {
    return next(new AppError('Booking not found', 404));
  }

  if (status === 'successful') {
    // Create escrow
    const escrow = await paymentService.createEscrow(booking);
    
    // Update booking
    booking.paymentStatus = 'completed';
    booking.status = 'confirmed';
    booking.paymentDetails.escrowId = escrow.escrowId;
    booking.paymentDetails.paymentDate = new Date();
    await booking.save();

    // Send notifications
    await notificationService.sendPaymentConfirmation(booking);
    await notificationService.sendBookingConfirmation(booking);
  } else {
    booking.paymentStatus = 'failed';
    booking.status = 'cancelled';
    booking.cancellationReason = 'Payment failed';
    await booking.save();
  }

  res.status(200).json({
    success: true,
    message: 'Webhook processed successfully'
  });
});

exports.getUserBookings = catchAsync(async (req, res, next) => {
  // Only show bookings that are properly confirmed or in progress
  // Exclude bookings with failed/cancelled payments or status
  const bookings = await Booking.find({ 
    user: req.user._id,
    $or: [
      // Confirmed bookings with successful payments
      { 
        status: { $in: ['confirmed', 'in_progress', 'completed', 'reviewed'] },
        paymentStatus: 'completed'
      },
      // Pending bookings that are still being processed (allow some time for payment)
      {
        status: 'pending',
        paymentStatus: { $in: ['pending', 'processing'] },
        createdAt: { $gte: new Date(Date.now() - 15 * 60 * 1000) } // Only show pending for 15 minutes
      }
    ]
  })
    .populate('barber', 'fullName businessName profileImage')
    .populate('service', 'name price duration')
    .select('+phoneNumber +address') // Include contact information
    .sort('-createdAt');

  res.status(200).json({
    success: true,
    data: {
      bookings: bookings
    }
  });
});

// Cleanup abandoned bookings endpoint (for admin/cron use)
exports.cleanupAbandonedBookings = catchAsync(async (req, res, next) => {
  try {
    // Mark old pending bookings as cancelled if they're older than 15 minutes
    // These are likely abandoned payments
    const cutoffTime = new Date(Date.now() - 15 * 60 * 1000); // 15 minutes ago
    
    const result = await Booking.updateMany(
      {
        status: 'pending',
        paymentStatus: { $in: ['pending', 'processing'] },
        createdAt: { $lt: cutoffTime }
      },
      {
        status: 'cancelled',
        paymentStatus: 'cancelled',
        cancellationReason: 'Payment timeout - booking abandoned',
        cancelledAt: new Date()
      }
    );

    res.status(200).json({
      success: true,
      message: `Cleaned up ${result.modifiedCount} abandoned bookings`,
      data: {
        modifiedCount: result.modifiedCount
      }
    });
  } catch (error) {
    console.error('Error cleaning up abandoned bookings:', error);
    return next(new AppError('Failed to cleanup abandoned bookings', 500));
  }
});

exports.getBarberBookings = catchAsync(async (req, res, next) => {
  const bookings = await Booking.find({ barber: req.user._id })
    .populate('user', 'fullName profileImage email phone')
    .populate('service', 'name price duration')
    .select('+phoneNumber +address') // Include customer contact information
    .sort('-createdAt');

  res.status(200).json({
    success: true,
    data: {
      bookings: bookings
    }
  });
});

exports.updateBookingStatus = catchAsync(async (req, res, next) => {
  const { status } = req.body;
  const booking = await Booking.findById(req.params.id)
    .populate('user barber service');

  if (!booking) {
    return next(new AppError('Booking not found', 404));
  }

  // Check if barber owns this booking
  if (booking.barber._id.toString() !== req.user._id.toString()) {
    return next(new AppError('Not authorized to update this booking', 403));
  }

  // Validate status transition
  const validTransitions = {
    confirmed: ['in_progress', 'cancelled'],
    in_progress: ['completed', 'cancelled'],
    completed: [],
    cancelled: []
  };

  if (!validTransitions[booking.status].includes(status)) {
    return next(new AppError(`Cannot transition from ${booking.status} to ${status}`, 400));
  }

  booking.status = status;
  if (status === 'completed') {
    booking.completedAt = new Date();
    // Release escrow payment
    await paymentService.releaseEscrow(booking.paymentDetails.escrowId);
  }

  await booking.save();

  // Send notifications
  await notificationService.sendBookingStatusUpdate(booking);

  res.status(200).json({
    success: true,
    data: booking
  });
});

exports.cancelBooking = catchAsync(async (req, res, next) => {
  const { reason } = req.body;
  const booking = await Booking.findById(req.params.id)
    .populate('user barber service');

  if (!booking) {
    return next(new AppError('Booking not found', 404));
  }

  // Check authorization
  if (booking.user._id.toString() !== req.user._id.toString() &&
      booking.barber._id.toString() !== req.user._id.toString()) {
    return next(new AppError('Not authorized to cancel this booking', 403));
  }

  // Check if booking can be cancelled
  if (!['pending', 'confirmed', 'pending_payment'].includes(booking.status)) {
    return next(new AppError(`Cannot cancel booking in ${booking.status} status`, 400));
  }

  booking.status = 'cancelled';
  booking.cancellationReason = reason;
  booking.cancelledBy = req.user.role;
  booking.cancelledAt = new Date();

  await booking.save();

  // Process refund if payment was made
  if (booking.paymentStatus === 'completed') {
    await paymentService.processRefund(booking);
  }

  // Send notifications
  await notificationService.sendBookingCancellation(booking);

  res.status(200).json({
    success: true,
    data: booking
  });
});

exports.submitReview = catchAsync(async (req, res, next) => {
  const { rating, review } = req.body;
  const booking = await Booking.findById(req.params.id)
    .populate('barber service');

  if (!booking) {
    return next(new AppError('Booking not found', 404));
  }

  // Check if user owns this booking
  if (booking.user.toString() !== req.user._id.toString()) {
    return next(new AppError('Not authorized to review this booking', 403));
  }

  // Check if booking is completed
  if (booking.status !== 'completed') {
    return next(new AppError('Can only review completed bookings', 400));
  }

  // Check if already reviewed
  if (booking.review && booking.review.rating) {
    return next(new AppError('Booking already reviewed', 400));
  }

  // Update booking with review
  booking.review = {
    rating: rating,
    comment: review,
    createdAt: new Date()
  };
  await booking.save();

  // Calculate and update barber's average rating
  const barberBookings = await Booking.find({
    barber: booking.barber._id,
    'review.rating': { $exists: true }
  });

  if (barberBookings.length > 0) {
    const averageRating = barberBookings.reduce((acc, curr) => acc + curr.review.rating, 0) / barberBookings.length;
    
    // Update barber's rating and total reviews
    await Barber.findByIdAndUpdate(booking.barber._id, {
      rating: Math.round(averageRating * 10) / 10, // Round to 1 decimal place
      totalReviews: barberBookings.length
    });
  }

  res.status(200).json({
    success: true,
    data: booking
  });
});

module.exports = {
  checkTimeSlotAvailability: exports.checkTimeSlotAvailability,
  getAvailableTimeSlots: exports.getAvailableTimeSlots,
  createBooking: exports.createBooking,
  handlePaymentWebhook: exports.handlePaymentWebhook,
  getUserBookings: exports.getUserBookings,
  getBarberBookings: exports.getBarberBookings,
  updateBookingStatus: exports.updateBookingStatus,
  cancelBooking: exports.cancelBooking,
  submitReview: exports.submitReview,
  cleanupAbandonedBookings: exports.cleanupAbandonedBookings
}; 