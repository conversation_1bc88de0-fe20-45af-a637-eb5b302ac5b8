const Payment = require('../models/Payment');
const Booking = require('../models/Booking');
const TransactionHistory = require('../models/TransactionHistory');
const { catchAsync, AppError } = require('../utils/errorHandlers');
const paymentService = require('../utils/paymentService');
const notificationService = require('../utils/notificationService');
const { validatePaymentMethod } = require('../middleware/validation');
const Barber = require('../models/Barber');

exports.initiatePayment = async (req, res) => {
  try {
    const { bookingId } = req.body;

    // Get booking details
    const booking = await Booking.findById(bookingId)
      .populate('user')
      .populate('barber')
      .populate('service');

    if (!booking) {
      return res.status(404).json({
        success: false,
        message: 'Booking not found'
      });
    }

    // Check if payment already exists
    const existingPayment = await Payment.findOne({ booking: bookingId });
    if (existingPayment) {
      return res.status(400).json({
        success: false,
        message: 'Payment already exists for this booking'
      });
    }

    // Initialize payment with Monnify
    const paymentData = await paymentService.initializeTransaction({
      amount: booking.totalPrice,
      userId: booking.user._id,
      barberId: booking.barber._id,
      bookingId: booking._id,
      customerEmail: booking.user.email,
      customerName: booking.user.name
    });

    // Update booking status
    booking.paymentStatus = 'processing';
    booking.paymentReference = paymentData.paymentReference;
    booking.transactionReference = paymentData.transactionReference;
    await booking.save();

    res.status(200).json({
      success: true,
      message: 'Payment initialized successfully',
      data: {
        paymentId: paymentData.paymentId,
        paymentUrl: paymentData.checkoutUrl,
        transactionReference: paymentData.transactionReference,
        amount: paymentData.amount
      }
    });
  } catch (error) {
    console.error('Payment initiation failed:', error);
    res.status(500).json({
      success: false,
      message: 'Payment initiation failed',
      error: error.message
    });
  }
};

exports.confirmServiceCompletion = async (req, res) => {
  try {
    const { paymentId } = req.params;
    const { isCompleted, review } = req.body;
    const userId = req.user._id;

    // Check if paymentId is a valid MongoDB ObjectId format
    const isValidObjectId = /^[0-9a-fA-F]{24}$/.test(paymentId);
    
    // Find payment by reference or ID based on format
    let payment;
    if (isValidObjectId) {
      payment = await Payment.findOne({
        $or: [
          { _id: paymentId },
          { paymentReference: paymentId },
          { transactionReference: paymentId }
        ]
      }).populate('booking user barber');
    } else {
      // If not a valid ObjectId, search only by reference fields
      payment = await Payment.findOne({
        $or: [
          { paymentReference: paymentId },
          { transactionReference: paymentId }
        ]
      }).populate('booking user barber');
    }

    if (!payment) {
      return res.status(404).json({
        success: false,
        message: 'Payment not found'
      });
    }

    // Verify user owns this payment
    if (payment.user._id.toString() !== userId.toString()) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to complete this payment'
      });
    }

    // Check if payment is in held status
    if (payment.status !== 'held') {
      return res.status(400).json({
        success: false,
        message: 'Payment is not eligible for completion'
      });
    }

    if (isCompleted) {
      // Get the booking
      const booking = await Booking.findById(payment.booking._id)
        .populate('user barber service');

      if (!booking) {
        return res.status(404).json({
          success: false,
          message: 'Booking not found'
        });
      }

      // Update booking status and review if provided
      // Only update status if it's not already completed
      if (booking.status !== 'completed') {
        booking.status = 'completed';
      }
      
      // Always update/add review if provided (allow updating existing reviews)
      if (review) {
        booking.review = {
          rating: review.rating,
          comment: review.comment,
          createdAt: new Date()
        };
      }
      await booking.save();

      // Update barber rating if review was provided
      if (review) {
        // Calculate and update barber's average rating
        const barberBookings = await Booking.find({
          barber: booking.barber._id,
          'review.rating': { $exists: true }
        });

        if (barberBookings.length > 0) {
          const averageRating = barberBookings.reduce((acc, curr) => acc + curr.review.rating, 0) / barberBookings.length;
          
          // Update barber's rating and total reviews
          await Barber.findByIdAndUpdate(booking.barber._id, {
            rating: Math.round(averageRating * 10) / 10, // Round to 1 decimal place
            totalReviews: barberBookings.length
          });
        }
      }

      // Release payment to barber (92% after 8% commission)
      // Only release if not already released
      if (payment.status === 'held') {
        await paymentService.releasePayment(payment._id);
      } else {
        console.log(`Payment ${payment._id} already released or not in held status: ${payment.status}`);
      }

      // Update payment metadata
      payment.metadata.serviceCompleted = true;
      payment.metadata.userConfirmed = true;
      if (review) {
        payment.metadata.userRated = true;
      }
      await payment.save();

      // Send credit alert email to barber
      const emailService = require('../utils/emailService');
      await emailService.sendBarberCreditNotification(
        payment.barber.email,
        payment.barber.fullName || payment.barber.name,
        {
          amount: payment.barberAmount,
          originalAmount: payment.amount,
          commission: payment.platformCommission,
          customerName: payment.user.fullName || payment.user.name,
          serviceName: booking.service.name,
          appointmentDate: booking.date,
          appointmentTime: booking.startTime
        }
      );

      res.status(200).json({
        success: true,
        message: 'Service completed successfully and payment released to barber',
        data: {
          paymentId: payment._id,
          status: payment.status,
          booking: {
            id: booking._id,
            status: booking.status,
            review: booking.review
          }
        }
      });
    } else {
      // Just update metadata if not completed
      const updatedPayment = await paymentService.updatePaymentMetadata(paymentId, {
        serviceCompleted: isCompleted
      });

      res.status(200).json({
        success: true,
        message: 'Service completion status updated',
        data: {
          paymentId: updatedPayment._id,
          status: updatedPayment.status,
          metadata: updatedPayment.metadata
        }
      });
    }
  } catch (error) {
    console.error('Service completion update failed:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update service completion status',
      error: error.message
    });
  }
};

exports.confirmUserSatisfaction = async (req, res) => {
  try {
    const { paymentId } = req.params;
    const { isConfirmed } = req.body;

    const payment = await paymentService.updatePaymentMetadata(paymentId, {
      userConfirmed: isConfirmed
    });

    res.status(200).json({
      success: true,
      message: 'User satisfaction status updated',
      data: {
        paymentId: payment._id,
        status: payment.status,
        metadata: payment.metadata
      }
    });
  } catch (error) {
    console.error('User satisfaction update failed:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update user satisfaction status',
      error: error.message
    });
  }
};

exports.getPaymentStatus = async (req, res) => {
  try {
    const { paymentId } = req.params;
    let payment = null;

    // Check if paymentId is a valid MongoDB ObjectId (24 character hex string)
    const isValidObjectId = /^[0-9a-fA-F]{24}$/.test(paymentId);
    
    if (isValidObjectId) {
      // Try to find by payment ID first if it's a valid ObjectId
      payment = await Payment.findById(paymentId);
    }
    
    if (!payment) {
      // Try finding by payment reference or transaction reference
      payment = await Payment.findOne({
        $or: [
          { paymentReference: paymentId },
          { transactionReference: paymentId }
        ]
      }).populate('booking');
    }

    if (!payment) {
      return res.status(404).json({
        success: false,
        message: 'Payment not found'
      });
    }

    // If payment is still pending, try to verify with Monnify
    if (payment.status === 'pending' && payment.transactionReference) {
      try {
        const paymentStatus = await paymentService.getPaymentStatus(payment._id);
        payment = paymentStatus; // Use updated payment status
      } catch (verifyError) {
        console.error('Payment verification error:', verifyError);
      }
    }

    res.status(200).json({
      success: true,
      data: {
        _id: payment._id,
        status: payment.status,
        amount: payment.amount,
        paymentReference: payment.paymentReference,
        transactionReference: payment.transactionReference,
        booking: payment.booking,
        createdAt: payment.createdAt,
        paidAt: payment.paidAt,
        releaseEligibleAt: payment.releaseEligibleAt,
        autoReleaseAt: payment.autoReleaseAt
      }
    });
  } catch (error) {
    console.error('Payment status fetch failed:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch payment status',
      error: error.message
    });
  }
};

exports.getBarberPayments = async (req, res) => {
  try {
    const barberId = req.params.barberId;
    const { status, startDate, endDate } = req.query;

    const query = { barber: barberId };
    
    if (status) {
      query.status = status;
    }
    
    if (startDate && endDate) {
      query.createdAt = {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      };
    }

    const payments = await Payment.find(query)
      .populate('booking')
      .sort({ createdAt: -1 });

    const totalEarnings = payments.reduce((sum, payment) => {
      if (payment.status === 'released' || payment.status === 'paid') {
        return sum + payment.barberAmount;
      }
      return sum;
    }, 0);

    res.status(200).json({
      success: true,
      data: {
        payments,
        totalEarnings,
        count: payments.length
      }
    });
  } catch (error) {
    console.error('Barber payments fetch failed:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch barber payments',
      error: error.message
    });
  }
};

exports.initializePayment = catchAsync(async (req, res, next) => {
  const { amount, currency, description } = req.body;

  if (!amount || !currency) {
    return next(new AppError('Amount and currency are required', 400));
  }

  const payment = await paymentService.initializePayment({
    amount,
    currency,
    description,
    userId: req.user._id
  });

  res.status(200).json({
    success: true,
    data: payment
  });
});

exports.verifyPayment = catchAsync(async (req, res, next) => {
  const { reference } = req.params;

  const payment = await Payment.findOne({ reference });
  if (!payment) {
    return next(new AppError('Payment not found', 404));
  }

  const verificationResult = await paymentService.verifyPayment(reference);
  if (!verificationResult.success) {
    return next(new AppError(verificationResult.message, 400));
  }

  payment.status = verificationResult.status;
  payment.verifiedAt = new Date();
  await payment.save();

  // Send success or failure notifications based on payment status
  if (payment.status === 'held' || payment.status === 'completed') {
    await notificationService.sendPaymentConfirmation(payment);
  } else if (payment.status === 'failed') {
    await notificationService.sendPaymentFailureNotification(payment);
  }

  res.status(200).json({
    success: true,
    data: payment
  });
});

exports.getPaymentHistory = catchAsync(async (req, res, next) => {
  const payments = await Payment.find({ user: req.user._id })
    .sort('-createdAt')
    .limit(10);

  res.status(200).json({
    success: true,
    data: payments
  });
});

exports.getPaymentDetails = catchAsync(async (req, res, next) => {
  const payment = await Payment.findById(req.params.id);
  if (!payment) {
    return next(new AppError('Payment not found', 404));
  }

  // Check if user owns this payment
  if (payment.user.toString() !== req.user._id.toString()) {
    return next(new AppError('Not authorized to view this payment', 403));
  }

  res.status(200).json({
    success: true,
    data: payment
  });
});

exports.handleWebhook = catchAsync(async (req, res, next) => {
  console.log('Received Monnify webhook:', req.body);
  
  try {
    // Process the webhook event
    await paymentService.handleWebhook(req.body);
    
    res.status(200).json({
      success: true,
      message: 'Webhook processed successfully'
    });
  } catch (error) {
    console.error('Webhook processing failed:', error);
    res.status(500).json({
      success: false,
      message: 'Webhook processing failed',
      error: error.message
    });
  }
});

exports.cancelPayment = catchAsync(async (req, res, next) => {
  const { paymentReference } = req.params;
  
  try {
    const payment = await paymentService.handlePaymentCancellation(paymentReference);
    
    res.status(200).json({
      success: true,
      message: 'Payment cancelled successfully',
      data: payment
    });
  } catch (error) {
    console.error('Payment cancellation failed:', error);
    res.status(400).json({
      success: false,
      message: error.message || 'Failed to cancel payment'
    });
  }
});

// Get user transaction history
exports.getUserTransactionHistory = catchAsync(async (req, res, next) => {
  try {
    const userId = req.user._id;
    const transactions = await paymentService.getUserTransactionHistory(userId);
    
    res.status(200).json({
      success: true,
      data: {
        transactions,
        count: transactions.length
      }
    });
  } catch (error) {
    console.error('Transaction history fetch failed:', error);
    return next(new AppError('Failed to fetch transaction history', 500));
  }
});

// Get barber transaction history (includes both payment transactions and withdrawal transactions)
exports.getBarberTransactionHistory = catchAsync(async (req, res, next) => {
  try {
    const barberId = req.user._id;
    
    // Get payment transactions from TransactionHistory
    const paymentTransactions = await paymentService.getBarberTransactionHistory(barberId);
    
    // Add transaction type to payment transactions for frontend distinction
    const enhancedPaymentTransactions = paymentTransactions.map(transaction => ({
      ...transaction.toObject(),
      transactionType: 'service_payment' // Mark as service payment transactions
    }));
    
    // Get withdrawal transactions from BarberEarnings
    const BarberEarnings = require('../models/BarberEarnings');
    const earnings = await BarberEarnings.findOne({ barber: barberId });
    let withdrawalTransactions = [];
    
    if (earnings && earnings.transactions) {
      // Transform withdrawal transactions to match the expected format
      withdrawalTransactions = earnings.transactions
        .filter(t => t.type === 'WITHDRAWAL')
        .map(t => ({
          _id: t._id,
          createdAt: t.createdAt,
          amount: Math.abs(t.amount), // Make positive for display
          status: t.description.includes('(PENDING)') ? 'pending' : 
                  t.description.includes('(COMPLETED)') ? 'success' : 
                  t.description.includes('(REJECTED)') ? 'failed' : 'pending',
          transactionReference: t.reference,
          paymentReference: t.reference,
          barberAmount: Math.abs(t.amount),
          commissionAmount: 0, // No commission on withdrawals
          isHeldInEscrow: false,
          type: 'withdrawal', // This is the key field to distinguish transaction types
          description: t.description,
          transactionType: 'withdrawal', // Additional field for frontend
          // For withdrawals, we don't need user/booking data since they're internal transactions
          user: null,
          booking: null
        }));
    }
    
    // Merge and sort all transactions by date (newest first)
    const allTransactions = [...enhancedPaymentTransactions, ...withdrawalTransactions]
      .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
    
    // Calculate total earnings from successful payment transactions only
    const totalEarnings = enhancedPaymentTransactions.reduce((sum, transaction) => {
      if (transaction.status === 'success' && !transaction.isHeldInEscrow) {
        return sum + transaction.barberAmount;
      }
      return sum;
    }, 0);
    
    // Calculate pending earnings (held in escrow) from payment transactions only
    const pendingEarnings = enhancedPaymentTransactions.reduce((sum, transaction) => {
      if (transaction.status === 'success' && transaction.isHeldInEscrow) {
        return sum + transaction.barberAmount;
      }
      return sum;
    }, 0);
    
    res.status(200).json({
      success: true,
      data: {
        transactions: allTransactions,
        totalEarnings,
        pendingEarnings,
        count: allTransactions.length // Total count includes withdrawals
      }
    });
  } catch (error) {
    console.error('Barber transaction history fetch failed:', error);
    return next(new AppError('Failed to fetch transaction history', 500));
  }
});

// Verify booking payment status by reference (similar to subscription verification)
exports.verifyBookingPayment = catchAsync(async (req, res, next) => {
  const { paymentReference } = req.params;
  
  // Basic validation of payment reference format
  if (!paymentReference || !paymentReference.startsWith('ETCH-')) {
    return next(new AppError('Invalid payment reference format', 400));
  }
  
  console.log('Verifying booking payment for reference:', paymentReference);
  
  try {
    // Check if this is an authenticated request (user verifying their own payment)
    let userId = null;
    if (req.user && req.user._id) {
      userId = req.user._id;
      console.log('Authenticated user verification for:', userId);
    }
    
    // Verify payment with payment service, passing user ID if available
    const verificationResult = await paymentService.verifyBookingPayment(paymentReference, userId);
    
    console.log('Booking verification result:', verificationResult);
    
    if (verificationResult.success) {
      res.status(200).json({
        success: true,
        status: 'success',
        data: verificationResult.data
      });
    } else {
      res.status(200).json({
        success: false,
        status: verificationResult.status,
        message: verificationResult.message
      });
    }
  } catch (error) {
    console.error('Booking payment verification failed:', error);
    return next(new AppError('Booking payment verification failed: ' + error.message, 500));
  }
});

// Legacy verify payment status method (kept for backward compatibility)
exports.verifyPaymentStatus = catchAsync(async (req, res, next) => {
  const { paymentReference } = req.params;
  
  // Basic validation of payment reference format
  if (!paymentReference || !paymentReference.startsWith('ETCH-')) {
    return next(new AppError('Invalid payment reference format', 400));
  }
  
  // Find payment by reference
  const payment = await paymentService.getPaymentByReference(paymentReference);
  
  if (!payment) {
    return next(new AppError('Payment not found', 404));
  }
  
  // Verify payment status with Monnify
  const updatedPayment = await paymentService.getPaymentStatus(payment._id);
  
  // Get booking details
  const booking = await Booking.findById(updatedPayment.booking)
    .populate('user barber service');
  
  // No need to send notifications here as they are already sent in getPaymentStatus
  // when it calls handlePaymentSuccess or handlePaymentFailure
  
  res.status(200).json({
    success: true,
    data: {
      payment: {
        id: updatedPayment._id,
        status: updatedPayment.status,
        amount: updatedPayment.amount,
        paymentReference: updatedPayment.paymentReference,
        transactionReference: updatedPayment.transactionReference,
        paidAt: updatedPayment.paidAt
      },
      booking: booking ? {
        id: booking._id,
        status: booking.status,
        paymentStatus: booking.paymentStatus,
        date: booking.date,
        startTime: booking.startTime,
        endTime: booking.endTime,
        barber: booking.barber ? {
          id: booking.barber._id,
          name: booking.barber.fullName || booking.barber.name,
          businessName: booking.barber.businessName
        } : null,
        service: booking.service ? {
          id: booking.service._id,
          name: booking.service.name,
          price: booking.service.price,
          duration: booking.service.duration
        } : null
      } : null
    }
  });
});

// Manual sync for completed payments that weren't credited
exports.syncBarberEarnings = async (req, res) => {
  try {
    const { paymentId } = req.params;
    
    // Find payment by reference or ID
    const isValidObjectId = /^[0-9a-fA-F]{24}$/.test(paymentId);
    let payment;
    
    if (isValidObjectId) {
      payment = await Payment.findOne({
        $or: [
          { _id: paymentId },
          { paymentReference: paymentId },
          { transactionReference: paymentId }
        ]
      }).populate('booking user barber');
    } else {
      payment = await Payment.findOne({
        $or: [
          { paymentReference: paymentId },
          { transactionReference: paymentId }
        ]
      }).populate('booking user barber');
    }

    if (!payment) {
      return res.status(404).json({
        success: false,
        message: 'Payment not found'
      });
    }

    // Check if payment is completed/released but barber wasn't credited
    if (payment.status === 'released' || payment.status === 'held') {
      const BarberEarnings = require('../models/BarberEarnings');
      
      // Get or create barber earnings
      const barberEarnings = await BarberEarnings.getOrCreate(payment.barber._id);
      
      console.log('Current barber earnings:', {
        barberId: payment.barber._id,
        availableBalance: barberEarnings.availableBalance,
        pendingBalance: barberEarnings.pendingBalance,
        totalEarnings: barberEarnings.totalEarnings,
        paymentAmount: payment.barberAmount
      });

      // Check if this payment was already credited
      const existingTransaction = barberEarnings.transactions.find(
        t => t.relatedPayment && t.relatedPayment.toString() === payment._id.toString()
      );

      if (existingTransaction) {
        return res.status(200).json({
          success: true,
          message: 'Payment already credited to barber',
          data: {
            payment: payment._id,
            barberEarnings: {
              availableBalance: barberEarnings.availableBalance,
              pendingBalance: barberEarnings.pendingBalance,
              totalEarnings: barberEarnings.totalEarnings
            },
            existingTransaction
          }
        });
      }

      // Credit the barber manually
      barberEarnings.availableBalance += payment.barberAmount;
      barberEarnings.totalEarnings += payment.barberAmount;

      // Add transaction record
      barberEarnings.addTransaction({
        type: 'SERVICE_PAYMENT',
        amount: payment.barberAmount,
        reference: payment.paymentReference || payment.transactionReference,
        description: `Manual credit for completed service - ₦${payment.amount} less ₦${payment.platformCommission} commission (8%)`,
        relatedPayment: payment._id,
        relatedBooking: payment.booking._id
      });

      await barberEarnings.save();

      // Update payment status to released if it was still held
      if (payment.status === 'held') {
        payment.status = 'released';
        payment.releasedAt = new Date();
        await payment.save();
      }

      // Send credit notification email to barber
      try {
        const emailService = require('../utils/emailService');
        await emailService.sendBarberCreditNotification(
          payment.barber.email,
          payment.barber.fullName || payment.barber.name,
          {
            amount: payment.barberAmount,
            originalAmount: payment.amount,
            commission: payment.platformCommission,
            customerName: payment.user.fullName || payment.user.name,
            serviceName: payment.booking.service ? payment.booking.service.name : 'Service',
            appointmentDate: payment.booking.date,
            appointmentTime: payment.booking.startTime
          }
        );
      } catch (emailError) {
        console.error('Failed to send credit notification email:', emailError);
        // Don't fail the whole operation if email fails
      }

      res.status(200).json({
        success: true,
        message: 'Barber earnings synced successfully',
        data: {
          payment: {
            id: payment._id,
            status: payment.status,
            amount: payment.amount,
            barberAmount: payment.barberAmount,
            platformCommission: payment.platformCommission
          },
          barberEarnings: {
            availableBalance: barberEarnings.availableBalance,
            pendingBalance: barberEarnings.pendingBalance,
            totalEarnings: barberEarnings.totalEarnings
          }
        }
      });
    } else {
      res.status(400).json({
        success: false,
        message: `Payment is not in a state that can be credited. Current status: ${payment.status}`
      });
    }
  } catch (error) {
    console.error('Barber earnings sync failed:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to sync barber earnings',
      error: error.message
    });
  }
}; 