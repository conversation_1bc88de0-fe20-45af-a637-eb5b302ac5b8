const Dispute = require('../models/Dispute');
const Booking = require('../models/Booking');
const disputeService = require('../utils/disputeService');
const { catchAsync, AppError } = require('../utils/errorHandlers');
const notificationService = require('../utils/notificationService');

exports.createDispute = catchAsync(async (req, res, next) => {
  const { bookingId, reason, description } = req.body;

  // Check if booking exists and belongs to user
  const booking = await Booking.findById(bookingId);
  if (!booking) {
    return next(new AppError('Booking not found', 404));
  }

  if (booking.user.toString() !== req.user._id.toString()) {
    return next(new AppError('Not authorized to create dispute for this booking', 403));
  }

  // Check if dispute already exists
  const existingDispute = await Dispute.findOne({ booking: bookingId });
  if (existingDispute) {
    return next(new AppError('Dispute already exists for this booking', 400));
  }

  const dispute = await Dispute.create({
    booking: bookingId,
    user: req.user._id,
    barber: booking.barber,
    reason,
    description,
    status: 'pending'
  });

  // Send notifications
  await notificationService.sendDisputeCreatedNotification(dispute);

  res.status(201).json({
    success: true,
    data: dispute
  });
});

exports.updateDisputeStatus = catchAsync(async (req, res, next) => {
  const { status, resolution, adminNotes } = req.body;

  // Only admin can update dispute status
  if (req.user.role !== 'admin') {
    return next(new AppError('Not authorized to update dispute status', 403));
  }

  const dispute = await Dispute.findById(req.params.disputeId);
  if (!dispute) {
    return next(new AppError('Dispute not found', 404));
  }

  dispute.status = status;
  dispute.resolution = resolution;
  dispute.adminNotes = adminNotes;
  dispute.resolvedAt = status === 'resolved' ? new Date() : null;
  await dispute.save();

  // Send notifications
  await notificationService.sendDisputeStatusUpdateNotification(dispute);

  res.status(200).json({
    success: true,
    data: dispute
  });
});

exports.resolveDispute = catchAsync(async (req, res, next) => {
  const { disputeId } = req.params;
  const { decision, refundAmount, notes } = req.body;
  const adminId = req.user._id;

  // Only admin can resolve dispute
  if (req.user.role !== 'admin') {
    return next(new AppError('Not authorized to resolve this dispute', 403));
  }

  const dispute = await disputeService.resolveDispute(
    disputeId,
    { decision, refundAmount, notes },
    adminId
  );

  res.status(200).json({
    success: true,
    message: 'Dispute resolved successfully',
    data: dispute
  });
});

exports.addAdminNote = catchAsync(async (req, res, next) => {
  const { disputeId } = req.params;
  const { note } = req.body;
  const adminId = req.user._id;

  // Only admin can add admin notes
  if (req.user.role !== 'admin') {
    return next(new AppError('Not authorized to add admin notes', 403));
  }

  const dispute = await disputeService.addAdminNote(disputeId, note, adminId);

  res.status(200).json({
    success: true,
    message: 'Admin note added successfully',
    data: dispute
  });
});

exports.addEvidence = catchAsync(async (req, res, next) => {
  const { disputeId } = req.params;
  const addedBy = req.user._id;

  // Handle file uploads
  if (!req.files || req.files.length === 0) {
    return next(new AppError('No evidence files provided', 400));
  }

  const evidenceUrls = req.files.map(file => file.path);
  const dispute = await disputeService.addEvidence(disputeId, evidenceUrls, addedBy);

  res.status(200).json({
    success: true,
    message: 'Evidence added successfully',
    data: dispute
  });
});

exports.getDisputeDetails = catchAsync(async (req, res, next) => {
  const dispute = await Dispute.findById(req.params.disputeId)
    .populate('booking')
    .populate('user', 'fullName email')
    .populate('barber', 'fullName email businessName');

  if (!dispute) {
    return next(new AppError('Dispute not found', 404));
  }

  // Check authorization
  if (dispute.user._id.toString() !== req.user._id.toString() && 
      dispute.barber._id.toString() !== req.user._id.toString() &&
      req.user.role !== 'admin') {
    return next(new AppError('Not authorized to view this dispute', 403));
  }

  res.status(200).json({
    success: true,
    data: dispute
  });
});

exports.getBookingDisputes = catchAsync(async (req, res, next) => {
  const { bookingId } = req.params;
  const disputes = await Dispute.find({ booking: bookingId })
    .populate('payment')
    .populate('resolvedBy', 'name email')
    .sort({ createdAt: -1 });

  res.status(200).json({
    success: true,
    data: disputes
  });
});

exports.addDisputeComment = catchAsync(async (req, res, next) => {
  const { comment } = req.body;

  const dispute = await Dispute.findById(req.params.disputeId);
  if (!dispute) {
    return next(new AppError('Dispute not found', 404));
  }

  // Check authorization
  if (dispute.user.toString() !== req.user._id.toString() && 
      dispute.barber.toString() !== req.user._id.toString() &&
      req.user.role !== 'admin') {
    return next(new AppError('Not authorized to comment on this dispute', 403));
  }

  dispute.comments.push({
    user: req.user._id,
    comment,
    userRole: req.user.role
  });

  await dispute.save();

  // Send notifications
  await notificationService.sendDisputeCommentNotification(dispute, comment);

  res.status(200).json({
    success: true,
    data: dispute
  });
});

exports.getMyDisputes = catchAsync(async (req, res, next) => {
  const disputes = await Dispute.find({
    $or: [
      { user: req.user._id },
      { barber: req.user._id }
    ]
  })
  .sort('-createdAt')
  .populate('booking')
  .limit(10);

  res.status(200).json({
    success: true,
    data: disputes
  });
});

// Get dispute statistics
exports.getDisputeStats = catchAsync(async (req, res, next) => {
  const stats = await Dispute.aggregate([
    {
      $group: {
        _id: "$status",
        count: { $sum: 1 }
      }
    }
  ]);

  // Transform the results into a more usable format
  const statusCounts = {
    open: 0,
    in_progress: 0,
    resolved: 0,
    closed: 0,
    total: 0
  };

  stats.forEach(item => {
    if (item._id) {
      statusCounts[item._id] = item.count;
      statusCounts.total += item.count;
    }
  });

  res.status(200).json({
    success: true,
    data: statusCounts
  });
});

module.exports = {
  createDispute: exports.createDispute,
  updateDisputeStatus: exports.updateDisputeStatus,
  resolveDispute: exports.resolveDispute,
  addAdminNote: exports.addAdminNote,
  addEvidence: exports.addEvidence,
  getDisputeDetails: exports.getDisputeDetails,
  getBookingDisputes: exports.getBookingDisputes,
  addDisputeComment: exports.addDisputeComment,
  getMyDisputes: exports.getMyDisputes,
  getDisputeStats: exports.getDisputeStats
}; 