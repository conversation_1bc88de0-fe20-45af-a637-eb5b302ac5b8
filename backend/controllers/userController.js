const { catchAsync } = require('../utils/errorHandlers');
const AppError = require('../utils/errorHandlers').AppError;
const User = require('../models/User');
const Barber = require('../models/Barber');
const { generateToken, invalidateUserTokens } = require('../middleware/auth');
const emailService = require('../utils/emailService');

/**
 * Get all favorite barbers for a user
 */
exports.getFavoriteBarbers = catchAsync(async (req, res, next) => {
  const userId = req.user._id;
  
  const user = await User.findById(userId)
    .populate({
      path: 'favorites',
      select: 'fullName businessName profile.profilePicture rating totalReviews services status isProfileActive',
      match: { 
        registrationStatus: 'verified', 
        status: 'active',
        isProfileActive: true // Only show barbers with active profiles
      }
    })
    .lean();

  if (!user) {
    return next(new AppError('User not found', 404));
  }

  // Transform data for frontend
  const favoriteBarbers = user.favorites ? user.favorites.map(barber => ({
    _id: barber._id,
    fullName: barber.fullName,
    businessName: barber.businessName,
    profileImage: barber.profile?.profilePicture || null,
    rating: barber.rating || 0,
    totalReviews: barber.totalReviews || 0,
    services: barber.services || []
  })) : [];

  res.status(200).json({
    success: true,
    data: {
      barbers: favoriteBarbers
    }
  });
});

/**
 * Toggle a barber as favorite
 */
exports.toggleFavoriteBarber = catchAsync(async (req, res, next) => {
  const userId = req.user._id;
  const { barberId } = req.params;

  // Check if barber exists and is verified with active profile
  const barber = await Barber.findOne({ 
    _id: barberId, 
    registrationStatus: 'verified',
    status: 'active',
    isProfileActive: true // Only allow favorites for barbers with active profiles
  });

  if (!barber) {
    return next(new AppError('Barber not found or not available', 404));
  }

  // Find user
  const user = await User.findById(userId);
  if (!user) {
    return next(new AppError('User not found', 404));
  }

  // Check if barber is already in favorites
  const favoriteIndex = user.favorites.indexOf(barberId);
  let action;

  if (favoriteIndex === -1) {
    // Add to favorites
    user.favorites.push(barberId);
    action = 'added';
  } else {
    // Remove from favorites
    user.favorites.splice(favoriteIndex, 1);
    action = 'removed';
  }

  await user.save();

  res.status(200).json({
    success: true,
    message: `Barber ${action} ${action === 'added' ? 'to' : 'from'} favorites successfully`,
    data: {
      isFavorite: action === 'added'
    }
  });
});

/**
 * Get user profile
 */
exports.getUserProfile = catchAsync(async (req, res, next) => {
  const userId = req.user._id;

  const user = await User.findById(userId)
    .select('-password -otp')
    .lean();

  if (!user) {
    return next(new AppError('User not found', 404));
  }

  res.status(200).json({
    success: true,
    data: user
  });
});

/**
 * Update user profile
 */
exports.updateUserProfile = catchAsync(async (req, res, next) => {
  const userId = req.user._id;
  const { fullName, phoneNumber, address } = req.body;

  const user = await User.findById(userId);
  if (!user) {
    return next(new AppError('User not found', 404));
  }

  // Update fields
  if (fullName) user.fullName = fullName;
  if (phoneNumber) user.phoneNumber = phoneNumber;
  if (address) user.address = address;

  await user.save();

  res.status(200).json({
    success: true,
    message: 'Profile updated successfully',
    data: {
      fullName: user.fullName,
      email: user.email,
      phoneNumber: user.phoneNumber,
      address: user.address
    }
  });
});

/**
 * Change user password
 */
exports.changePassword = catchAsync(async (req, res, next) => {
  const userId = req.user._id;
  const { newPassword } = req.body;

  // Validate new password
  if (!newPassword) {
    return next(new AppError('New password is required', 400));
  }

  // Find user
  const user = await User.findById(userId);
  if (!user) {
    return next(new AppError('User not found', 404));
  }

  // Update password
  user.password = newPassword;
  await user.save();

  // Generate new token
  const token = generateToken(user._id, 'user');

  // Invalidate all other sessions
  await invalidateUserTokens(user._id);

  // Send password change confirmation email
  try {
    await emailService.sendPasswordChangeConfirmation(user.email, user.fullName);
  } catch (error) {
    console.error('Failed to send password change confirmation email:', error);
  }

  res.status(200).json({
    success: true,
    message: 'Password updated successfully',
    data: { token }
  });
}); 