const Payment = require('../models/Payment');
const refundService = require('../utils/refundService');

exports.initiateRefund = async (req, res) => {
  try {
    const { paymentId, reason } = req.body;

    // Get payment
    const payment = await Payment.findById(paymentId);
    if (!payment) {
      return res.status(404).json({
        success: false,
        message: 'Payment not found'
      });
    }

    // Validate refund eligibility
    await refundService.validateRefundEligibility(payment, reason);

    // Process refund
    const refund = await refundService.processRefund(paymentId, reason);

    res.status(200).json({
      success: true,
      message: 'Refund processed successfully',
      data: {
        refundId: refund._id,
        status: refund.status,
        amount: refund.amount,
        refundReference: refund.refundReference
      }
    });
  } catch (error) {
    console.error('Refund initiation failed:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to process refund',
      error: error.message
    });
  }
};

exports.getRefundStatus = async (req, res) => {
  try {
    const { refundReference } = req.params;
    const status = await refundService.getRefundStatus(refundReference);

    res.status(200).json({
      success: true,
      data: status
    });
  } catch (error) {
    console.error('Refund status fetch failed:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch refund status',
      error: error.message
    });
  }
};

exports.getBookingRefunds = async (req, res) => {
  try {
    const { bookingId } = req.params;
    const refunds = await Refund.find({ booking: bookingId })
      .populate('payment')
      .sort({ createdAt: -1 });

    res.status(200).json({
      success: true,
      data: refunds
    });
  } catch (error) {
    console.error('Booking refunds fetch failed:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch booking refunds',
      error: error.message
    });
  }
}; 