const { catchAsync } = require('../utils/errorHandlers');
const AppError = require('../utils/errorHandlers').AppError;
const Barber = require('../models/Barber');
const User = require('../models/User');
const VerificationRequest = require('../models/VerificationRequest');
const BarberEarnings = require('../models/BarberEarnings');
const TransactionHistory = require('../models/TransactionHistory');
const Withdrawal = require('../models/Withdrawal');
const Payment = require('../models/Payment');
const Refund = require('../models/Refund');
const Dispute = require('../models/Dispute');
const Subscription = require('../models/Subscription');
const { uploadToCloudinary, deleteFromCloudinary } = require('../config/cloudinary');
const emailService = require('../utils/emailService');
const Service = require('../models/Service');
const Schedule = require('../models/Schedule');
const Booking = require('../models/Booking');
const { generateOTP } = require('../utils/emailService');
const paymentConfig = require('../config/payment');

// Step 1: Personal Information Collection
exports.registerBarberStep1 = catchAsync(async (req, res, next) => {
  const { fullName, email, phoneNumber, address } = req.body;

  // Check if email already exists across all user types
  const existingUser = await User.findByEmail(email);
  const existingBarber = await Barber.findByEmail(email);
  
  if (existingUser || existingBarber) {
    return next(new AppError('Email is already registered', 400));
  }

  // Create new barber record with step1_completed status
  const barber = new Barber({
    fullName,
    email,
    phoneNumber,
    address,
    registrationStatus: 'step1_completed'
  });

  await barber.save();

  // Return success response with barberId for next step
  res.status(201).json({
    success: true,
    message: 'Step 1 completed successfully. Please proceed to upload your business documents.',
    data: {
      barberId: barber._id,
      fullName: barber.fullName,
      email: barber.email,
      status: barber.registrationStatus,
      nextStep: 'step2',
      nextStepUrl: '/api/auth/register/barber/step2'
    }
  });
});

// Step 2: Business Documentation Upload
exports.registerBarberStep2 = catchAsync(async (req, res, next) => {
  const { barberId, businessName } = req.body;
  const files = req.files;

  // Find barber by ID
  const barber = await Barber.findById(barberId);
  if (!barber) {
    return next(new AppError('Barber not found', 404));
  }

  // Check if barber is in correct step
  if (barber.registrationStatus !== 'step1_completed') {
    return next(new AppError('Invalid registration step. Please complete step 1 first.', 400));
  }

  // Upload documents to Cloudinary
  const uploadPromises = [];
  const documentUrls = {};

  // Upload CAC Certificate
  if (files.cacCertificate && files.cacCertificate[0]) {
    const cacFile = files.cacCertificate[0];
    const resourceType = cacFile.mimetype.startsWith('application/pdf') ? 'raw' : 'image';
    uploadPromises.push(
      uploadToCloudinary(cacFile.buffer, {
        folder: `etch/barbers/${barberId}/documents`,
        tags: ['barber-document', 'cac-certificate'],
        publicId: `${barberId}_cac_certificate`,
        resourceType
      }).then(result => {
        documentUrls.cacCertificate = {
          url: result.secure_url,
          publicId: result.public_id,
          resourceType,
          originalName: cacFile.originalname,
          uploadedAt: new Date()
        };
      })
    );
  }

  // Upload NIN Document
  if (files.ninDocument && files.ninDocument[0]) {
    const ninFile = files.ninDocument[0];
    const resourceType = ninFile.mimetype.startsWith('application/pdf') ? 'raw' : 'image';
    uploadPromises.push(
      uploadToCloudinary(ninFile.buffer, {
        folder: `etch/barbers/${barberId}/documents`,
        tags: ['barber-document', 'nin-document'],
        publicId: `${barberId}_nin_document`,
        resourceType
      }).then(result => {
        documentUrls.ninDocument = {
          url: result.secure_url,
          publicId: result.public_id,
          resourceType,
          originalName: ninFile.originalname,
          uploadedAt: new Date()
        };
      })
    );
  }

  // Upload Passport Photo
  if (files.passportPhoto && files.passportPhoto[0]) {
    const passportFile = files.passportPhoto[0];
    uploadPromises.push(
      uploadToCloudinary(passportFile.buffer, {
        folder: `etch/barbers/${barberId}/documents`,
        tags: ['barber-document', 'passport-photo'],
        publicId: `${barberId}_passport_photo`,
        resourceType: 'image',
        transformation: [
          { width: 300, height: 300, crop: 'fill', gravity: 'face' },
          { quality: 'auto', fetch_format: 'auto' }
        ]
      }).then(result => {
        documentUrls.passportPhoto = {
          url: result.secure_url,
          publicId: result.public_id,
          resourceType: 'image',
          originalName: passportFile.originalname,
          uploadedAt: new Date()
        };
      })
    );
  }

  // Wait for all uploads to complete
  await Promise.all(uploadPromises);

  // Update barber record with business information and document URLs
  barber.businessName = businessName;
  barber.documents = documentUrls;
  barber.registrationStatus = 'step2_completed';

  await barber.save();

  // Return success response
  res.status(200).json({
    success: true,
    message: 'Step 2 completed successfully. Please proceed to set up your security credentials.',
    data: {
      barberId: barber._id,
      businessName: barber.businessName,
      documents: {
        cacCertificate: documentUrls.cacCertificate?.url,
        ninDocument: documentUrls.ninDocument?.url,
        passportPhoto: documentUrls.passportPhoto?.url
      },
      status: barber.registrationStatus,
      nextStep: 'step3',
      nextStepUrl: '/api/auth/register/barber/step3'
    }
  });
});

// Step 3: Security Setup & Account Verification
exports.registerBarberStep3 = catchAsync(async (req, res, next) => {
  const { barberId, password } = req.body;

  // Find barber by ID
  const barber = await Barber.findById(barberId);
  if (!barber) {
    return next(new AppError('Barber not found', 404));
  }

  // Check if barber is in correct step
  if (barber.registrationStatus !== 'step2_completed') {
    return next(new AppError('Invalid registration step. Please complete step 2 first.', 400));
  }

  // Set password and generate OTP
  barber.password = password;
  const otp = barber.generateOTP();
  barber.registrationStatus = 'pending_verification';
  await barber.save();

  // Send OTP email
  await emailService.sendBarberOTPEmail(barber.email, otp, barber.fullName);

  res.status(200).json({
    success: true,
    message: 'Security credentials set. Please verify your account with the OTP sent to your email.',
    data: {
      barberId: barber._id,
      email: barber.email,
      status: barber.registrationStatus,
      nextStep: 'verify',
      nextStepUrl: '/api/auth/register/barber/verify'
    }
  });
});

// Verify OTP and Complete Registration
exports.verifyBarberOTP = catchAsync(async (req, res, next) => {
  try {
    const { barberId, email, otp } = req.body;

    // Find barber by ID
    const barber = barberId 
      ? await Barber.findById(barberId)
      : await Barber.findOne({ email });

    if (!barber) {
      return next(new AppError('Barber not found', 404));
    }

    // Check if barber has OTP
    if (!barber.otp || !barber.otp.code) {
      return next(new AppError('No OTP found. Please request a new one.', 400));
    }

    // Verify OTP
    const verificationResult = barber.verifyOTP(otp);
    if (!verificationResult.success) {
      return next(new AppError(verificationResult.message, 400));
    }

    // Update barber status
    barber.registrationStatus = 'pending_verification';
    barber.emailVerifiedAt = new Date();
    barber.otp = { code: null, expiresAt: null };
    await barber.save();

    // Create verification request
    const verificationRequest = new VerificationRequest({
      barberId: barber._id,
      status: 'pending',
      requestType: 'initial_verification',
      priority: 'normal',
      slaDeadline: new Date(Date.now() + 48 * 60 * 60 * 1000) // 48 hours from now
    });
    await verificationRequest.save();

    // Send notification to admin
    await emailService.sendAdminBarberRegistrationNotification(barber);

    res.status(200).json({
      success: true,
      message: 'Account verified successfully. Your registration is now pending verification.',
      data: {
        barberId: barber._id,
        email: barber.email,
        status: barber.registrationStatus,
        verificationRequestId: verificationRequest._id
      }
    });
  } catch (error) {
    console.error('Error in verifyBarberOTP:', error);
    return next(new AppError('Failed to verify OTP. Please try again.', 500));
  }
});

// Resend OTP
exports.resendBarberOTP = catchAsync(async (req, res, next) => {
  console.log('Resend OTP Request Body:', req.body);
  const { barberId, email } = req.body;

  // Log what we extracted
  console.log('Extracted values:', { barberId, email });

  // Find barber by ID or email
  const barber = barberId 
    ? await Barber.findById(barberId)
    : await Barber.findOne({ email });

  if (!barber) {
    return next(new AppError('Barber not found', 404));
  }

  // Check if resend is allowed (only if we have lastAttempt)
  if (barber.otp && barber.otp.lastAttempt && Date.now() - barber.otp.lastAttempt < 60000) {
    return next(new AppError('Please wait 1 minute before requesting a new OTP', 429));
  }

  // Generate and send new OTP
  const otp = barber.generateOTP();
  await barber.save();

  // Send OTP email
  await emailService.sendBarberOTPEmail(barber.email, otp, barber.fullName);

  res.status(200).json({
    success: true,
    message: 'New OTP has been sent to your email',
    data: {
      barberId: barber._id,
      email: barber.email
    }
  });
});

// Search Barbers
exports.searchBarbers = catchAsync(async (req, res, next) => {
  const { query, serviceArea, service, rating, availability } = req.query;
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10;
  const skip = (page - 1) * limit;

  // Build search filter - only show verified and active profiles
  const filter = { 
    registrationStatus: 'verified',
    isProfileActive: true 
  };
  if (query) {
    filter.$or = [
      { fullName: new RegExp(query, 'i') },
      { businessName: new RegExp(query, 'i') }
    ];
  }
  
  // Get barbers
  let barbers = await Barber.find(filter)
    .select('fullName email phoneNumber businessName address registrationStatus isProfileActive profile rating totalReviews')
    .populate('services')
    .sort('-rating')
    .skip(skip)
    .limit(limit)
    .lean();

  // Apply service area filter with more flexible matching
  if (serviceArea) {
    // Split the search term to handle cases like "Sango" or "Sango, Ibadan"
    const searchTerms = serviceArea.toLowerCase().split(/[,\s]+/).filter(term => term.trim());
    
    barbers = barbers.filter(barber => {
      if (!barber.profile?.serviceArea) return false;
      
      const barberServiceArea = barber.profile.serviceArea.toLowerCase();
      // Check if any of the search terms are included in the barber's service area
      return searchTerms.some(term => barberServiceArea.includes(term));
    });
  }

  // Apply service filter after populating services
  if (service) {
    barbers = barbers.filter(barber => 
      barber.services && 
      barber.services.some(s => 
        s.category && s.category.toLowerCase().includes(service.toLowerCase())
      )
    );
  }

  // Apply rating filter after fetching
  if (rating) {
    barbers = barbers.filter(barber => barber.rating >= parseFloat(rating));
  }

  // Transform data for frontend
  const transformedBarbers = barbers.map(barber => ({
    _id: barber._id,
    fullName: barber.fullName,
    email: barber.email,
    phoneNumber: barber.phoneNumber,
    businessName: barber.businessName,
    address: barber.address,
    registrationStatus: barber.registrationStatus,
    isProfileActive: barber.isProfileActive,
    profile: barber.profile || {},
    services: barber.services || [],
    profileImage: barber.profile?.profilePicture || barber.documents?.passportPhoto?.url || null,
    rating: barber.rating || 0,
    totalReviews: barber.totalReviews || 0,
    status: 'active',
    serviceArea: barber.profile?.serviceArea || ''
  }));

  // Get total count - adjusted for post-filtering
  const total = barbers.length;

  res.status(200).json({
    success: true,
    data: {
      barbers: transformedBarbers,
      pagination: {
        page,
        pages: Math.ceil(total / limit),
        total
      }
    }
  });
});

// Get Barber Profile
exports.getBarberProfile = catchAsync(async (req, res, next) => {
  try {
    console.log(`Fetching barber profile for ID: ${req.params.barberId}`);
    
    // Validate barberId format
    if (!req.params.barberId.match(/^[0-9a-fA-F]{24}$/)) {
      return next(new AppError('Invalid barber ID format', 400));
    }
    
    // First fetch the barber without populating services
    const barber = await Barber.findById(req.params.barberId)
      .select('-password -otp');

    if (!barber) {
      console.log(`Barber not found with ID: ${req.params.barberId}`);
      return next(new AppError('Barber not found', 404));
    }

    // Check if profile is active (public endpoint, should only show active profiles)
    if (!barber.isProfileActive) {
      console.log(`Barber profile is inactive: ${req.params.barberId}`);
      return next(new AppError('Barber profile is currently unavailable', 404));
    }

    console.log(`Barber found: ${barber.fullName}`);

    // Fetch services separately
    let services = [];
    try {
      services = await Service.find({ barber: barber._id });
      console.log(`Found ${services.length} services for barber`);
    } catch (serviceError) {
      console.error('Error fetching services:', serviceError);
      // Continue without services if there's an error
    }

    // Format the response to include all necessary information
    const formattedBarber = {
      _id: barber._id,
      fullName: barber.fullName,
      email: barber.email,
      phoneNumber: barber.phoneNumber,
      businessName: barber.businessName || '',
      address: barber.address || '',
      registrationStatus: barber.registrationStatus,
      isProfileActive: barber.isProfileActive,
      profile: barber.profile || {},
      services: services || [],
      profileImage: barber.profile?.profilePicture || null,
      rating: barber.rating || 0,
      totalReviews: barber.totalReviews || 0,
      status: barber.status || 'active',
      serviceArea: barber.profile?.serviceArea || ''
    };

    res.status(200).json({
      success: true,
      data: formattedBarber
    });
  } catch (error) {
    console.error('Error in getBarberProfile:', error);
    return next(new AppError('Failed to fetch barber profile', 500));
  }
});

// Update Barber Profile
exports.updateBarberProfile = catchAsync(async (req, res, next) => {
  const { bio, experience, serviceArea } = req.body;
  const barberId = req.user._id;

  const barber = await Barber.findById(barberId);
  if (!barber) {
    return next(new AppError('Barber not found', 404));
  }

  // Initialize profile if it doesn't exist
  if (!barber.profile) {
    barber.profile = {};
  }

  // Update bio and experience
  if (bio !== undefined) barber.profile.bio = bio;
  if (experience !== undefined) barber.profile.experience = experience;
  if (serviceArea !== undefined) barber.profile.serviceArea = serviceArea;
  
  // Note: Business information (businessName and location) cannot be changed by barbers
  // after registration for security reasons

  // Handle profile picture upload
  if (req.files && req.files.profilePicture && req.files.profilePicture.length > 0) {
    const profilePictureFile = req.files.profilePicture[0];
    
    // Delete old profile picture if exists
    if (barber.profile.profilePicture && barber.profile.profilePicturePublicId) {
      try {
        await deleteFromCloudinary(barber.profile.profilePicturePublicId);
      } catch (error) {
        console.error('Failed to delete old profile picture:', error);
        // Continue with upload even if deletion fails
      }
    }

    // Upload new profile picture
    const uploadOptions = {
      folder: `etch/barbers/${barberId}/profile`,
      tags: ['barber-profile-picture'],
      resourceType: 'image',
      transformation: [
        { width: 400, height: 400, crop: 'fill', gravity: 'face' },
        { quality: 'auto', fetch_format: 'auto' }
      ]
    };

    try {
      const result = await uploadToCloudinary(profilePictureFile.buffer, uploadOptions);
      barber.profile.profilePicture = result.secure_url;
      barber.profile.profilePicturePublicId = result.public_id;
    } catch (error) {
      console.error('Profile picture upload failed:', error);
      return next(new AppError('Failed to upload profile picture', 500));
    }
  }

  // Handle portfolio uploads
  if (req.files && req.files.portfolio && req.files.portfolio.length > 0) {
    const uploadPromises = [];
    const portfolioItems = [];

    // Process each uploaded file
    for (const file of req.files.portfolio) {
      const isVideo = file.mimetype.startsWith('video/');
      const resourceType = isVideo ? 'video' : 'image';
      
      const uploadOptions = {
        folder: `etch/barbers/${barberId}/portfolio`,
        tags: ['barber-portfolio'],
        resourceType
      };

      // Add transformation for images
      if (!isVideo) {
        uploadOptions.transformation = [
          { width: 800, height: 600, crop: 'limit' },
          { quality: 'auto', fetch_format: 'auto' }
        ];
      }

      uploadPromises.push(
        uploadToCloudinary(file.buffer, uploadOptions).then(result => {
          portfolioItems.push({
            url: result.secure_url,
            publicId: result.public_id,
            type: isVideo ? 'video' : 'image',
            uploadedAt: new Date()
          });
        })
      );
    }

    // Wait for all uploads to complete
    await Promise.all(uploadPromises);

    // Initialize portfolio array if it doesn't exist
    if (!barber.profile.portfolio) {
      barber.profile.portfolio = [];
    }

    // Add new portfolio items (limit to 10 total)
    barber.profile.portfolio = [...barber.profile.portfolio, ...portfolioItems].slice(0, 10);
  }

  await barber.save();

  // Return updated profile data
  const updatedBarber = await Barber.findById(barberId)
    .select('fullName profile businessName isProfileActive')
    .lean();

  res.status(200).json({
    success: true,
    message: 'Profile updated successfully',
    data: {
      firstName: updatedBarber.fullName.split(' ')[0],
      lastName: updatedBarber.fullName.split(' ').slice(1).join(' '),
      bio: updatedBarber.profile?.bio || '',
      experience: updatedBarber.profile?.experience || '',
      serviceArea: updatedBarber.profile?.serviceArea || '',
      portfolio: updatedBarber.profile?.portfolio || [],
      businessName: updatedBarber.businessName || '',
      location: updatedBarber.profile?.location?.address || 'Ibadan, Nigeria',
      profilePicture: updatedBarber.profile?.profilePicture || '',
      isProfileActive: updatedBarber.isProfileActive
    }
  });
});

// Update Profile Status (Active/Inactive)
exports.updateProfileStatus = catchAsync(async (req, res, next) => {
  const barberId = req.user._id;
  const { isActive } = req.body;

  // Validate input
  if (typeof isActive !== 'boolean') {
    return next(new AppError('isActive must be a boolean value', 400));
  }

  const subscription = await Subscription.getOrCreate(barberId);

  // If trying to activate profile, check subscription status
  if (isActive) {
    if (!subscription.isActive) {
      let message = 'You need an active subscription to make your profile visible to customers.';
      
      if (subscription.status === 'never_subscribed') {
        message = `Subscribe for ₦${paymentConfig.subscriptionFee.toLocaleString()}/month to activate your profile.`;
      } else if (subscription.status === 'expired') {
        message = `Your subscription has expired. Please renew your ₦${paymentConfig.subscriptionFee.toLocaleString()} monthly subscription to make your profile visible.`;
      } else if (subscription.status === 'suspended') {
        message = 'Your subscription is suspended. Please contact support to reactivate your account.';
      } else if (subscription.status === 'cancelled') {
        message = 'Your subscription was cancelled. Please renew your subscription to make your profile visible.';
      }

      return res.status(400).json({
        success: false,
        message,
        data: {
          isProfileActive: false,
          subscriptionRequired: true,
          subscriptionStatus: subscription.status,
          daysRemaining: subscription.daysRemaining,
          currentPeriod: subscription.currentPeriod,
          subscriptionFee: paymentConfig.subscriptionFee
        }
      });
    }
  }

  // If deactivating profile, always allow it regardless of subscription status
  // This gives barbers control to turn off their profile when needed

  // Update the barber's profile status
  const updatedBarber = await Barber.findByIdAndUpdate(
    barberId,
    { isProfileActive: isActive },
    { new: true, runValidators: true }
  ).select('isProfileActive fullName businessName');

  if (!updatedBarber) {
    return next(new AppError('Barber not found', 404));
  }

  res.status(200).json({
    success: true,
    message: `Profile ${isActive ? 'activated' : 'deactivated'} successfully`,
    data: {
      isProfileActive: updatedBarber.isProfileActive,
      status: isActive ? 'Active' : 'Inactive'
    }
  });
});

// Add Service
exports.addService = catchAsync(async (req, res, next) => {
  const { name, description, duration, category, priceAdult, priceKid } = req.body;
  const barberId = req.user._id;

  // Validate category
  const validCategories = ['Haircuts', 'Beard Services', 'Hair Coloring', 'Hair Line Shaping'];
  if (!validCategories.includes(category)) {
    return next(new AppError('Invalid service category', 400));
  }

  // Handle price values
  const adultPrice = Number(priceAdult);
  if (isNaN(adultPrice) || adultPrice < 0) {
    return next(new AppError('Adult price must be a valid number greater than or equal to 0', 400));
  }

  // Handle kid price - set to 0 if empty string, undefined, or invalid
  let kidPrice = 0;
  if (priceKid !== undefined && priceKid !== '') {
    kidPrice = Number(priceKid);
    if (isNaN(kidPrice) || kidPrice < 0) {
      return next(new AppError('Kid price must be a valid number greater than or equal to 0', 400));
    }
  }

  const service = await Service.create({
    barber: barberId,
    name,
    description,
    duration,
    category,
    priceAdult: adultPrice,
    priceKid: kidPrice
  });

  res.status(201).json({
    success: true,
    message: 'Service added successfully',
    data: service
  });
});

// Update Service
exports.updateService = catchAsync(async (req, res, next) => {
  const { name, description, duration, category, priceAdult, priceKid, isActive } = req.body;
  const serviceId = req.params.serviceId;
  const barberId = req.user._id;

  const service = await Service.findOne({
    _id: serviceId,
    barber: barberId
  });

  if (!service) {
    return next(new AppError('Service not found', 404));
  }

  // Validate category if provided
  if (category) {
    const validCategories = ['Haircuts', 'Beard Services', 'Hair Coloring', 'Hair Line Shaping'];
    if (!validCategories.includes(category)) {
      return next(new AppError('Invalid service category', 400));
    }
  }

  // Handle adult price if provided
  if (priceAdult !== undefined) {
    const adultPrice = Number(priceAdult);
    if (isNaN(adultPrice) || adultPrice < 0) {
      return next(new AppError('Adult price must be a valid number greater than or equal to 0', 400));
    }
    service.priceAdult = adultPrice;
  }

  // Handle kid price if provided
  if (priceKid !== undefined) {
    if (priceKid === '') {
      service.priceKid = 0;
    } else {
      const kidPrice = Number(priceKid);
      if (isNaN(kidPrice) || kidPrice < 0) {
        return next(new AppError('Kid price must be a valid number greater than or equal to 0', 400));
      }
      service.priceKid = kidPrice;
    }
  }

  // Update other fields
  if (name) service.name = name;
  if (description) service.description = description;
  if (duration) service.duration = Number(duration);
  if (category) service.category = category;
  if (isActive !== undefined) service.isActive = isActive;

  await service.save();

  res.status(200).json({
    success: true,
    message: 'Service updated successfully',
    data: service
  });
});

// Get Barber Services
exports.getBarberServices = catchAsync(async (req, res, next) => {
  const barberId = req.user._id;

  const services = await Service.find({ barber: barberId }).sort('category');

  // Group services by category
  const servicesByCategory = {};
  services.forEach(service => {
    if (!servicesByCategory[service.category]) {
      servicesByCategory[service.category] = [];
    }
    servicesByCategory[service.category].push(service);
  });

  res.status(200).json({
    success: true,
    data: {
      services,
      servicesByCategory
    }
  });
});

// Delete Service
exports.deleteService = catchAsync(async (req, res, next) => {
  const serviceId = req.params.serviceId;
  const barberId = req.user._id;

  const service = await Service.findOneAndDelete({
    _id: serviceId,
    barber: barberId
  });

  if (!service) {
    return next(new AppError('Service not found', 404));
  }

  res.status(200).json({
    success: true,
    message: 'Service deleted successfully'
  });
});

// Get all verified barbers with pagination and filters (excluding banned)
exports.getVerifiedBarbers = catchAsync(async (req, res, next) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10;
  const status = req.query.status || 'all';
  const skip = (page - 1) * limit;

  // Build query - show only active verified barbers with visible profiles (exclude banned)
  let query = { 
    registrationStatus: 'verified',
    isProfileActive: true, // Only show barbers with active profiles
    $or: [
      { status: { $ne: 'banned' } },
      { status: { $exists: false } }
    ]
  };
  
  // Add status filter if not 'all' (but still exclude banned)
  if (status !== 'all') {
    if (status === 'active') {
      query.$or = [
        { status: 'active' },
        { status: { $exists: false } }
      ];
    }
  }

  // Execute query with pagination
  const [barbers, total] = await Promise.all([
    Barber.find(query)
      .select('fullName email phoneNumber businessName status address profile isProfileActive registrationStatus createdAt rating totalReviews')
      .skip(skip)
      .limit(limit)
      .sort({ createdAt: -1 }) // Show newest barbers first
      .lean(),
    Barber.countDocuments(query)
  ]);

  // Get service counts for all barbers
  const barberIds = barbers.map(barber => barber._id);
  
  // Get service counts in parallel for better performance
  const serviceCountPromises = barberIds.map(barberId => 
    Service.countDocuments({ 
      barber: barberId
      // Count all services, not just active ones for admin view
    })
  );
  
  const serviceCounts = await Promise.all(serviceCountPromises);
  
  // Create a map for quick lookup
  const serviceCountMap = {};
  barberIds.forEach((barberId, index) => {
    serviceCountMap[barberId.toString()] = serviceCounts[index];
  });

  // Transform data for frontend
  const transformedBarbers = barbers.map(barber => {
    // Validate profile image URL
    let profileImage = null;
    const img = barber.profile?.profilePicture;
    if (img && 
        typeof img === 'string' && 
        !img.includes('test_photo') && 
        !img.includes('undefined') &&
        (img.startsWith('http') || img.startsWith('https'))) {
      profileImage = img;
    }

    return {
      _id: barber._id,
      fullName: barber.fullName,
      name: barber.fullName,
      email: barber.email,
      phone: barber.phoneNumber,
      businessName: barber.businessName,
      status: barber.status || 'active',
      isProfileActive: barber.isProfileActive !== undefined ? barber.isProfileActive : false,
      profileImage,
      services: serviceCountMap[barber._id.toString()] || 0,
      address: barber.address,
      registrationStatus: barber.registrationStatus,
      joinedDate: barber.createdAt,
      rating: barber.rating || 0,
      totalReviews: barber.totalReviews || 0,
      profile: {
        location: barber.profile?.location || {},
        bio: barber.profile?.bio || '',
        experience: barber.profile?.experience || ''
      }
    };
  });

  res.status(200).json({
    success: true,
    data: {
      barbers: transformedBarbers,
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(total / limit),
        totalItems: total,
        itemsPerPage: limit
      }
    }
  });
});

// Get banned barbers with pagination
exports.getBannedBarbers = catchAsync(async (req, res, next) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10;
  const skip = (page - 1) * limit;

  // Build query - show only banned verified barbers
  let query = { 
    registrationStatus: 'verified',
    status: 'banned'
  };

  // Execute query with pagination
  const [barbers, total] = await Promise.all([
    Barber.find(query)
      .select('fullName email phoneNumber businessName status address profile isProfileActive registrationStatus createdAt rating totalReviews')
      .skip(skip)
      .limit(limit)
      .sort({ updatedAt: -1 }) // Show recently banned first
      .lean(),
    Barber.countDocuments(query)
  ]);

  // Get service counts for all barbers
  const barberIds = barbers.map(barber => barber._id);
  
  // Get service counts in parallel for better performance
  const serviceCountPromises = barberIds.map(barberId => 
    Service.countDocuments({ 
      barber: barberId
    })
  );
  
  const serviceCounts = await Promise.all(serviceCountPromises);
  
  // Create a map for quick lookup
  const serviceCountMap = {};
  barberIds.forEach((barberId, index) => {
    serviceCountMap[barberId.toString()] = serviceCounts[index];
  });

  // Transform data for frontend
  const transformedBarbers = barbers.map(barber => {
    // Validate profile image URL
    let profileImage = null;
    const img = barber.profile?.profilePicture;
    if (img && 
        typeof img === 'string' && 
        !img.includes('test_photo') && 
        !img.includes('undefined') &&
        (img.startsWith('http') || img.startsWith('https'))) {
      profileImage = img;
    }

    return {
      _id: barber._id,
      fullName: barber.fullName,
      name: barber.fullName,
      email: barber.email,
      phone: barber.phoneNumber,
      businessName: barber.businessName,
      status: barber.status || 'banned',
      isProfileActive: barber.isProfileActive !== undefined ? barber.isProfileActive : false,
      profileImage,
      services: serviceCountMap[barber._id.toString()] || 0,
      address: barber.address,
      registrationStatus: barber.registrationStatus,
      joinedDate: barber.createdAt,
      bannedDate: barber.updatedAt, // When they were banned
      rating: barber.rating || 0,
      totalReviews: barber.totalReviews || 0,
      profile: {
        location: barber.profile?.location || {},
        bio: barber.profile?.bio || '',
        experience: barber.profile?.experience || ''
      }
    };
  });

  res.status(200).json({
    success: true,
    data: {
      barbers: transformedBarbers,
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(total / limit),
        totalItems: total,
        itemsPerPage: limit
      }
    }
  });
});

// Ban/Unban a barber
exports.toggleBarberBan = catchAsync(async (req, res, next) => {
  const { barberId } = req.params;
  const { reason, action } = req.body;

  const barber = await Barber.findById(barberId);
  if (!barber) {
    return next(new AppError('Barber not found', 404));
  }

  const currentStatus = barber.status || 'active';
  let newStatus;
  let actionPerformed;

  // Determine the action based on current status
  if (currentStatus === 'banned') {
    // If currently banned, unban them
    newStatus = 'active';
    actionPerformed = 'unbanned';
  } else {
    // If currently active (or any other status), ban them
    newStatus = 'banned';
    actionPerformed = 'banned';
    
    // For banning, require a reason
    if (!reason || !reason.trim()) {
      return next(new AppError('Reason is required for banning a barber', 400));
    }
  }

  // Update the status
  barber.status = newStatus;
  await barber.save();

  // Send email notification to barber
  try {
    const emailService = require('../utils/emailService');
    
    if (actionPerformed === 'banned') {
      // Send ban notification
      await emailService.sendAccountBanEmail(barber.email, {
        barberName: barber.fullName,
        reason: reason.trim(),
        contactEmail: process.env.SUPPORT_EMAIL || '<EMAIL>'
      });
      console.log(`Ban notification email sent to ${barber.email}`);
    } else {
      // Send unban notification
      await emailService.sendAccountUnbanEmail(barber.email, {
        barberName: barber.fullName,
        contactEmail: process.env.SUPPORT_EMAIL || '<EMAIL>'
      });
      console.log(`Unban notification email sent to ${barber.email}`);
    }
  } catch (emailError) {
    console.error('Failed to send email notification:', emailError);
    // Continue without throwing error - the ban/unban was successful
  }

  res.status(200).json({
    success: true,
    message: `Barber ${actionPerformed} successfully`,
    data: {
      barberId: barber._id,
      status: newStatus,
      action: actionPerformed
    }
  });
});

// Delete a barber
exports.deleteBarber = catchAsync(async (req, res, next) => {
  const { barberId } = req.params;
  const { reason } = req.body;

  const barber = await Barber.findById(barberId);
  if (!barber) {
    return next(new AppError('Barber not found', 404));
  }

  // Send deletion notification email before deleting
  try {
    const emailService = require('../utils/emailService');
    
    await emailService.sendAccountDeletionEmail(barber.email, {
      barberName: barber.fullName,
      reason: reason || 'Account deletion requested by administration',
      contactEmail: process.env.SUPPORT_EMAIL || '<EMAIL>'
    });
    
    console.log(`Account deletion notification email sent to ${barber.email}`);
  } catch (emailError) {
    console.error('Failed to send deletion notification email:', emailError);
    // Continue with deletion even if email fails
  }

  try {
    console.log(`🗑️ Starting complete deletion of barber: ${barber.fullName} (${barber.email})`);

    // Step 1: Delete associated documents from Cloudinary
    console.log('📁 Deleting documents from Cloudinary...');
    if (barber.documents) {
      const deletePromises = [];
      Object.values(barber.documents).forEach(doc => {
        if (doc && doc.publicId) {
          deletePromises.push(deleteFromCloudinary(doc.publicId));
        }
      });
      if (deletePromises.length > 0) {
        await Promise.all(deletePromises);
        console.log(`✅ Deleted ${deletePromises.length} document(s) from Cloudinary`);
      }
    }

    // Step 2: Delete profile images from Cloudinary
    console.log('🖼️ Deleting profile images from Cloudinary...');
    if (barber.profile?.profilePicturePublicId) {
      await deleteFromCloudinary(barber.profile.profilePicturePublicId);
      console.log('✅ Deleted profile picture from Cloudinary');
    }

    // Step 3: Delete portfolio images
    console.log('🎨 Deleting portfolio images from Cloudinary...');
    if (barber.profile?.portfolio && barber.profile.portfolio.length > 0) {
      const portfolioDeletePromises = barber.profile.portfolio
        .filter(item => item.publicId)
        .map(item => deleteFromCloudinary(item.publicId));
      
      if (portfolioDeletePromises.length > 0) {
        await Promise.all(portfolioDeletePromises);
        console.log(`✅ Deleted ${portfolioDeletePromises.length} portfolio image(s) from Cloudinary`);
      }
    }

    // Step 4: Find and delete all related payments first (before deleting bookings)
    console.log('💳 Finding and deleting related payments...');
    const barberPayments = await Payment.find({ barber: barberId });
    const paymentIds = barberPayments.map(payment => payment._id);
    console.log(`Found ${barberPayments.length} payments to delete`);

    // Step 5: Delete refunds related to the barber's payments
    if (paymentIds.length > 0) {
      console.log('🔄 Deleting related refunds...');
      const deletedRefunds = await Refund.deleteMany({ payment: { $in: paymentIds } });
      console.log(`✅ Deleted ${deletedRefunds.deletedCount} refund(s)`);

      // Step 6: Delete disputes related to the barber's payments
      console.log('⚖️ Deleting related disputes...');
      const deletedDisputes = await Dispute.deleteMany({ 
        $or: [
          { payment: { $in: paymentIds } },
          { initiator: barberId, initiatorType: 'Barber' }
        ]
      });
      console.log(`✅ Deleted ${deletedDisputes.deletedCount} dispute(s)`);
    }

    // Step 7: Delete all payments related to this barber
    const deletedPayments = await Payment.deleteMany({ barber: barberId });
    console.log(`✅ Deleted ${deletedPayments.deletedCount} payment(s)`);

    // Step 8: Delete all associated services
    console.log('🔧 Deleting barber services...');
    const deletedServices = await Service.deleteMany({ barber: barberId });
    console.log(`✅ Deleted ${deletedServices.deletedCount} service(s)`);

    // Step 9: Delete barber earnings record
    console.log('💰 Deleting barber earnings...');
    const deletedEarnings = await BarberEarnings.deleteMany({ barber: barberId });
    console.log(`✅ Deleted ${deletedEarnings.deletedCount} earnings record(s)`);

    // Step 10: Delete transaction history
    console.log('📊 Deleting transaction history...');
    const deletedTransactions = await TransactionHistory.deleteMany({ barber: barberId });
    console.log(`✅ Deleted ${deletedTransactions.deletedCount} transaction(s)`);

    // Step 11: Delete any pending withdrawals
    console.log('🏦 Deleting pending withdrawals...');
    const deletedWithdrawals = await Withdrawal.deleteMany({ barber: barberId });
    console.log(`✅ Deleted ${deletedWithdrawals.deletedCount} withdrawal(s)`);

    // Step 12: Delete any verification requests
    console.log('📋 Deleting verification requests...');
    const deletedVerificationRequests = await VerificationRequest.deleteMany({ barberId: barberId });
    console.log(`✅ Deleted ${deletedVerificationRequests.deletedCount} verification request(s)`);

    // Step 13: Delete schedules related to this barber
    console.log('📅 Deleting barber schedules...');
    const deletedSchedules = await Schedule.deleteMany({ barber: barberId });
    console.log(`✅ Deleted ${deletedSchedules.deletedCount} schedule(s)`);

    // Step 14: Delete subscriptions related to this barber
    console.log('🔔 Deleting barber subscriptions...');
    const deletedSubscriptions = await Subscription.deleteMany({ barber: barberId });
    console.log(`✅ Deleted ${deletedSubscriptions.deletedCount} subscription(s)`);

    // Step 15: Delete bookings related to this barber
    console.log('📅 Deleting bookings...');
    const deletedBookings = await Booking.deleteMany({ barber: barberId });
    console.log(`✅ Deleted ${deletedBookings.deletedCount} booking(s)`);

    // Step 16: Remove barber from any user's favorites list
    console.log('❤️ Removing from user favorites...');
    const updatedUsers = await User.updateMany(
      { favoriteBarbers: barberId },
      { $pull: { favoriteBarbers: barberId } }
    );
    console.log(`✅ Removed from ${updatedUsers.modifiedCount} user's favorites`);

    // Step 17: Finally, delete the barber account itself
    console.log('👤 Deleting barber account...');
    await barber.deleteOne();
    console.log('✅ Barber account deleted successfully');

    console.log(`🎉 Complete deletion successful for barber: ${barber.fullName}`);

    res.status(200).json({
      success: true,
      message: 'Barber and all associated data deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting barber:', error);
    return next(new AppError('Failed to delete barber completely. Some data may remain.', 500));
  }
});

// Get Barber Settings
exports.getBarberSettings = catchAsync(async (req, res, next) => {
  const barberId = req.user._id;

  const barber = await Barber.findById(barberId)
    .select('fullName email phoneNumber address notificationPreferences');

  if (!barber) {
    return next(new AppError('Barber not found', 404));
  }

  res.status(200).json({
    success: true,
    data: {
      personalInfo: {
        firstName: barber.fullName.split(' ')[0],
        lastName: barber.fullName.split(' ').slice(1).join(' '),
        email: barber.email,
        phone: barber.phoneNumber,
        address: barber.address
      },
      notificationPreferences: barber.notificationPreferences || {
        bookingAlerts: {
          email: true,
          push: true,
          sms: false
        },
        customerMessages: {
          email: true,
          push: true,
          sms: true
        },
        paymentNotifications: {
          email: true,
          push: false,
          sms: true
        }
      }
    }
  });
});

// Update Personal Information
exports.updatePersonalInfo = catchAsync(async (req, res, next) => {
  const barberId = req.user._id;
  const { firstName, lastName, phone, address } = req.body;

  const barber = await Barber.findById(barberId);
  if (!barber) {
    return next(new AppError('Barber not found', 404));
  }

  // Update fields
  if (firstName || lastName) {
    barber.fullName = `${firstName || ''} ${lastName || ''}`.trim();
  }
  if (phone) barber.phoneNumber = phone;
  if (address) barber.address = address;

  await barber.save();

  res.status(200).json({
    success: true,
    message: 'Personal information updated successfully',
    data: {
      firstName: barber.fullName.split(' ')[0],
      lastName: barber.fullName.split(' ').slice(1).join(' '),
      email: barber.email,
      phone: barber.phoneNumber,
      address: barber.address
    }
  });
});

// Update Notification Preferences
exports.updateNotificationPreferences = catchAsync(async (req, res, next) => {
  const barberId = req.user._id;
  const { notificationPreferences } = req.body;

  const barber = await Barber.findById(barberId);
  if (!barber) {
    return next(new AppError('Barber not found', 404));
  }

  barber.notificationPreferences = notificationPreferences;
  await barber.save();

  res.status(200).json({
    success: true,
    message: 'Notification preferences updated successfully',
    data: barber.notificationPreferences
  });
});

// Update Password
exports.updatePassword = catchAsync(async (req, res, next) => {
  const barberId = req.user._id;
  const { currentPassword, newPassword } = req.body;

  const barber = await Barber.findById(barberId).select('+password');
  if (!barber) {
    return next(new AppError('Barber not found', 404));
  }

  // Check current password
  const isPasswordValid = await barber.comparePassword(currentPassword);
  if (!isPasswordValid) {
    return next(new AppError('Current password is incorrect', 401));
  }

  // Update password
  barber.password = newPassword;
  await barber.save();

  res.status(200).json({
    success: true,
    message: 'Password updated successfully'
  });
});

// Get Barber Profile Data for Management
exports.getBarberProfileData = catchAsync(async (req, res, next) => {
  const barberId = req.user._id;

  const barber = await Barber.findById(barberId)
    .select('fullName profile registrationStatus businessName isProfileActive')
    .lean();

  if (!barber) {
    return next(new AppError('Barber not found', 404));
  }

  // Get subscription status
  const subscription = await Subscription.getOrCreate(barberId);

  res.status(200).json({
    success: true,
    data: {
      firstName: barber.fullName.split(' ')[0],
      lastName: barber.fullName.split(' ').slice(1).join(' '),
      bio: barber.profile?.bio || '',
      experience: barber.profile?.experience || '',
      serviceArea: barber.profile?.serviceArea || '',
      portfolio: barber.profile?.portfolio || [],
      businessName: barber.businessName || '',
      location: barber.profile?.location?.address || 'Ibadan, Nigeria',
      registrationStatus: barber.registrationStatus,
      profilePicture: barber.profile?.profilePicture || '',
      isProfileActive: barber.isProfileActive !== undefined ? barber.isProfileActive : false,
      subscription: {
        status: subscription.status,
        isActive: subscription.isActive,
        daysRemaining: subscription.daysRemaining,
        currentPeriod: subscription.currentPeriod
      }
    }
  });
});

// Delete Portfolio Item
exports.deletePortfolioItem = catchAsync(async (req, res, next) => {
  const barberId = req.user._id;
  const { portfolioId } = req.params;

  const barber = await Barber.findById(barberId);
  if (!barber) {
    return next(new AppError('Barber not found', 404));
  }

  if (!barber.profile || !barber.profile.portfolio) {
    return next(new AppError('Portfolio not found', 404));
  }

  // Find the portfolio item
  const portfolioItem = barber.profile.portfolio.id(portfolioId);
  if (!portfolioItem) {
    return next(new AppError('Portfolio item not found', 404));
  }

  // Delete from Cloudinary
  if (portfolioItem.publicId) {
    await deleteFromCloudinary(portfolioItem.publicId);
  }

  // Remove from database
  barber.profile.portfolio.pull(portfolioId);
  await barber.save();

  res.status(200).json({
    success: true,
    message: 'Portfolio item deleted successfully'
  });
});

// Get Barber Earnings and Balance Information
exports.getBarberEarnings = catchAsync(async (req, res, next) => {
  try {
    const barberId = req.user._id;
    const earnings = await BarberEarnings.getOrCreate(barberId);
    
    // Get actual escrow amount from transaction history
    const escrowTransactions = await TransactionHistory.find({
      barber: barberId,
      status: 'success',
      isHeldInEscrow: true
    });
    
    // Calculate real pending amount in escrow
    const realPendingInEscrow = escrowTransactions.reduce((sum, transaction) => {
      return sum + (transaction.barberAmount || 0);
    }, 0);
    
    // Get pending withdrawals to calculate actual available balance
    const pendingWithdrawals = await Withdrawal.find({
      barber: barberId,
      status: 'pending'
    });

    // Calculate total pending withdrawal amount
    const totalPendingWithdrawals = pendingWithdrawals.reduce((sum, withdrawal) => {
      return sum + withdrawal.amount;
    }, 0);

    // Calculate actual available balance (available balance minus pending withdrawals)
    const actualAvailableBalance = Math.max(0, earnings.availableBalance - totalPendingWithdrawals);
    
    res.status(200).json({
      success: true,
      data: {
        availableBalance: earnings.availableBalance,
        pendingBalance: earnings.pendingBalance,
        realPendingInEscrow: realPendingInEscrow, // Real amount held in escrow
        totalEarnings: earnings.totalEarnings,
        totalWithdrawn: earnings.totalWithdrawn,
        lastWithdrawalDate: earnings.lastWithdrawalDate,
        pendingWithdrawals: totalPendingWithdrawals,
        actualAvailableBalance: actualAvailableBalance
      }
    });
  } catch (error) {
    console.error('Get barber earnings error:', error);
    return next(new AppError('Error fetching earnings information', 500));
  }
});

// Get barber reviews from completed bookings
exports.getBarberReviews = catchAsync(async (req, res, next) => {
  const { barberId } = req.params;
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 5;
  const skip = (page - 1) * limit;
  
  // Find the barber
  const barber = await Barber.findById(barberId);
  if (!barber) {
    return next(new AppError('Barber not found', 404));
  }
  
  // Find all completed bookings with reviews for this barber
  const bookings = await Booking.find({
    barber: barberId,
    status: 'completed',
    'review.rating': { $exists: true }
  })
  .populate('user', 'fullName profileImage')
  .populate('service', 'name')
  .select('review createdAt service')
  .sort({ 'review.createdAt': -1, createdAt: -1 })
  .skip(skip)
  .limit(limit);
  
  // Transform the data to a more usable format
  const reviews = bookings.map(booking => ({
    _id: booking._id,
    rating: booking.review.rating,
    comment: booking.review.comment,
    createdAt: booking.review.createdAt || booking.createdAt,
    user: booking.user,
    service: booking.service
  }));
  
  // Get total count for pagination
  const totalReviews = await Booking.countDocuments({
    barber: barberId,
    status: 'completed',
    'review.rating': { $exists: true }
  });
  
  const totalPages = Math.ceil(totalReviews / limit);
  
  res.status(200).json({
    success: true,
    data: {
      reviews,
      pagination: {
        page,
        pages: totalPages,
        total: totalReviews
      }
    }
  });
});

module.exports = {
  registerBarberStep1: exports.registerBarberStep1,
  registerBarberStep2: exports.registerBarberStep2,
  registerBarberStep3: exports.registerBarberStep3,
  verifyBarberOTP: exports.verifyBarberOTP,
  resendBarberOTP: exports.resendBarberOTP,
  searchBarbers: exports.searchBarbers,
  getBarberProfile: exports.getBarberProfile,
  getBarberProfileData: exports.getBarberProfileData,
  updateBarberProfile: exports.updateBarberProfile,
  updateProfileStatus: exports.updateProfileStatus,
  deletePortfolioItem: exports.deletePortfolioItem,
  addService: exports.addService,
  updateService: exports.updateService,
  deleteService: exports.deleteService,
  getVerifiedBarbers: exports.getVerifiedBarbers,
  getBannedBarbers: exports.getBannedBarbers,
  toggleBarberBan: exports.toggleBarberBan,
  deleteBarber: exports.deleteBarber,
  getBarberSettings: exports.getBarberSettings,
  updatePersonalInfo: exports.updatePersonalInfo,
  updateNotificationPreferences: exports.updateNotificationPreferences,
  updatePassword: exports.updatePassword,
  getBarberServices: exports.getBarberServices,
  getBarberEarnings: exports.getBarberEarnings,
  getBarberReviews: exports.getBarberReviews
};
